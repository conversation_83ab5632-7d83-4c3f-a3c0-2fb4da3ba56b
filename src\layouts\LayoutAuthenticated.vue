<script setup>
import { mdiFor<PERSON>burger, mdiBackburger, mdiMenu, mdiCash } from '@mdi/js'
import { ref, computed } from 'vue'
import menuAside from '@/menuAside.js'
import menuNavBar from '@/menuNavBar.js'
import { useDarkModeStore } from '@/stores/darkMode.js'
import { useServiceAccountsStore } from '@/stores/serviceAccounts'
import BaseIcon from '@/components/BaseIcon.vue'
import FormControl from '@/components/FormControl.vue'
import NavBar from '@/components/NavBar.vue'
import NavBarItemPlain from '@/components/NavBarItemPlain.vue'
import AsideMenu from '@/components/AsideMenu.vue'
import FooterBar from '@/components/FooterBar.vue'
import CardBoxModal from '@/components/CardBoxModal.vue'
import BaseButton from '@/components/BaseButton.vue'
import { useAuthStore } from '@/stores/auth'
import { useRoute, useRouter } from 'vue-router'

const layoutAsidePadding = 'xl:pl-52'
const layoutNavbarHeight = 'h-12'

const darkModeStore = useDarkModeStore()
const authStore = useAuthStore()
const serviceAccountStore = useServiceAccountsStore()

const accountData = computed(() => {
  return authStore.accountData
})

const floatData = computed(() => {
  return serviceAccountStore.floatData
})

const route = useRoute()
const router = useRouter()

const isAsideMobileExpanded = ref(false)
const isAsideLgActive = ref(false)
const isFloatModalActive = ref(false)

router.beforeEach(() => {
  isAsideMobileExpanded.value = false
  isAsideLgActive.value = false
})

const startFloatModal = async (status) => {
  await serviceAccountStore.getFloat(accountData.value.id)
  isFloatModalActive.value = status
}

const menuClick = (event, item) => {
  if (item.isToggleLightDark) {
    darkModeStore.set()
  }

  if (item.isLogout) {
    //
  }
}

defineProps(['title'])
</script>

<template>
  <div
    :class="{
      'overflow-hidden lg:overflow-visible': isAsideMobileExpanded
    }"
    class="flex flex-col min-h-screen"
  >
    <NavBar
      :menu="menuNavBar"
      :class="[layoutAsidePadding, layoutNavbarHeight, { 'ml-52 lg:ml-0': isAsideMobileExpanded }]"
      @menu-click="menuClick"
    >
      <NavBarItemPlain
        display="flex lg:hidden"
        @click.prevent="isAsideMobileExpanded = !isAsideMobileExpanded"
      >
        <BaseIcon :path="isAsideMobileExpanded ? mdiBackburger : mdiForwardburger" size="24" />
      </NavBarItemPlain>
      <NavBarItemPlain display="hidden lg:flex xl:hidden" @click.prevent="isAsideLgActive = true">
        <BaseIcon :path="mdiMenu" size="24" />
      </NavBarItemPlain>

      <template #right-content>
        <button
          v-if="route.path.startsWith('/account/')"
          @click="startFloatModal(true)"
          class="flex items-center text-md font-normal justify-center border px-3 py-2 bg-gray-100 rounded"
        >
          <BaseIcon
            :path="mdiCash"
            size="24"
            class="text-gray-600 rounded cursor-pointer mr-2"
            title="Check Float"
          />
          Check Float
        </button>
      </template>
    </NavBar>
    <div>
      <AsideMenu
        :is-aside-mobile-expanded="isAsideMobileExpanded"
        :is-aside-lg-active="isAsideLgActive"
        :menu="menuAside"
        @menu-click="menuClick"
        @aside-lg-close-click="isAsideLgActive = false"
      />
      <div
        :class="[layoutAsidePadding, { 'ml-52 lg:ml-0': isAsideMobileExpanded }]"
        class="flex-grow pt-14 w-screen transition-position lg:w-auto bg-gray-50 dark:bg-slate-800 dark:text-slate-100"
      >
        <slot />
      </div>
    </div>
    <div class="mt-auto">
      <FooterBar class="flex-shrink-0"> </FooterBar>
    </div>
  </div>
  <CardBoxModal
    v-model="isFloatModalActive"
    title=""
    button="info"
    @confirm="startFloatModal(false)"
  >
    <table class="text-sm mb-5">
      <tbody class="text-left">
        <tr class="flex">
          <td class="w-[15rem] font-[500] text-[#333] text-sm uppercase">MONEY</td>
          <td class="flex-grow">{{ floatData.money ?? '0' }} ZMW</td>
        </tr>
        <tr v-for="data in floatData.billers" :key="data.id" class="flex">
          <td class="w-[15rem] font-[500] text-[#333] text-sm">{{ data.service.name ?? 'N/A' }}</td>
          <td class="flex-grow">{{ data.amount ?? '0' }} ZMW</td>
        </tr>
      </tbody>
    </table>
  </CardBoxModal>
</template>
