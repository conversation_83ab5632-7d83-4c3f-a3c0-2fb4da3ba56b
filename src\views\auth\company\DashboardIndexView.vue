<template>
  <LayoutAuthenticated>
    <SectionMainFull>
      <CardBoxModal
        v-model="isFilterModalActive"
        title="Find Transaction"
        button="info"
        has-cancel
        button-label="Search"
        action-label="confirm"
        @confirm="filterDataFn()"
      >
        <div class="grid grid-cols-1 gap-2 lg:grid-cols-2">
          <FormField label="Start Date" help="">
            <FormControl
              v-model="filterData.init_start_date"
              type="datetime-local"
              name="start_date"
              placeholder="Start Date"
            />
          </FormField>
          <FormField label="End Date" help="">
            <FormControl
              v-model="filterData.init_end_date"
              type="datetime-local"
              name="start_date"
              placeholder="Start Date"
            />
          </FormField>
        </div>
        <div class="grid grid-cols-1 gap-2 lg:grid-cols-2">
          <FormField label="Status">
            <FormControl v-model="filterData.status" :options="statusOptions" />
          </FormField>
          <FormField label="Services">
            <FormControl v-model="filterData.service" :options="serviceOptions" />
          </FormField>
        </div>
        <div class="grid grid-cols-1 gap-2 lg:grid-cols-2">
          <FormField label="Currency">
            <FormControl v-model="filterData.currency" :options="currencyOptions" />
          </FormField>
          <FormField label="Transaction Type">
            <FormControl v-model="filterData.transaction_type" :options="transactionTypeOptions" />
          </FormField>
        </div>
        <div class="grid grid-cols-1 gap-2 lg:grid-cols-2">
          <FormField label="Phone Number" help="">
            <FormControl
              v-model="filterData.phone_number"
              type="tel"
              name="phone_number"
              placeholder="e.g. ************"
            />
          </FormField>
          <FormField label="Account Number" help="">
            <FormControl
              v-model="filterData.account_number"
              type="text"
              name="account_number"
              placeholder="e.g. ************"
            />
          </FormField>
        </div>
        <div class="grid grid-cols-1 gap-2 lg:grid-cols-2">
          <FormField label="Transaction ID" help="">
            <FormControl
              v-model="filterData.transaction_id"
              type="text"
              name="transaction_id"
              placeholder="e.g. QWERTY123456"
            />
          </FormField>
          <FormField label="External ID" help="">
            <FormControl
              v-model="filterData.external_id"
              type="text"
              name="external_id"
              placeholder="e.g. QWERTY123456"
            />
          </FormField>
        </div>
        <div class="grid grid-cols-1 gap-2 lg:grid-cols-2">
          <FormField label="Narration" help="">
            <FormControl
              v-model="filterData.narration"
              type="text"
              name="narration"
              placeholder="e.g. Payment description"
            />
          </FormField>
          <div></div>
          <!-- Empty div for alignment -->
        </div>
      </CardBoxModal>

      <!-- Date Range Display -->
      <div class="mb-4">
        <p class="text-gray-600 dark:text-gray-400 text-sm font-medium">
          {{ dateRangeText }}
        </p>
      </div>

      <div class="grid grid-cols-1 gap-6 lg:grid-cols-4 mb-6">
        <CardBoxWidget
          trend-type="down"
          color="text-blue-500"
          bgColor="bg-red-500"
          :icon="mdiCash"
          :number="transactionStore.total_amount"
          :count="transactionStore.total_count"
          prefix=""
          label="Total Payments"
          containClass="text-right border"
        />
        <CardBoxWidget
          trend-type="down"
          color="text-primeGreen"
          containClass="bg-primeGreen text-white text-right"
          :icon="mdiCash"
          :number="transactionStore.total_successful_amount"
          :count="transactionStore.total_successful_count"
          prefix=""
          label="Successful Payments"
        />
        <CardBoxWidget
          trend-type="down"
          color="text-primeTeal"
          :icon="mdiCash"
          :number="transactionStore.total_pending_amount"
          :count="transactionStore.total_pending_count"
          prefix=""
          label="Pending Payment"
          containClass="bg-primeTeal text-white text-right"
        />
        <CardBoxWidget
          trend-type="down"
          color="text-primeRed"
          :icon="mdiCash"
          :number="transactionStore.total_unsuccessful_amount"
          :count="transactionStore.total_unsuccessful_count"
          prefix=""
          label="Unsuccessful Payment"
          containClass="bg-primeRed text-white text-right"
        />
      </div>
      <div
        class="flex justify-items-end items-center space-x-5 border-b py-2 border-gray-100 mb-3"
        style="overflow: auto"
      >
        <BaseButton
          color="contrast"
          :icon="mdiFilterOutline"
          outline
          label="Find Record"
          @click="startFilterModal(true)"
        />
        <BaseButton
          v-if="items.length"
          color="contrast"
          :icon="mdiExport"
          outline
          label="Export"
          @click="exportTransactions()"
        />
        <BaseIcon
          @click="reloadData()"
          :path="mdiReload"
          size="30"
          w=""
          h="h-10"
          class="text-gray-600 p-1 h-full border border-black rounded cursor-pointer hover:bg-black hover:text-white"
          title="Reload"
        />
      </div>
      <CardBox rounded="rounded-sm" class="mb-6 px-2" has-table style="overflow: auto">
        <DatabaseLoaderOne v-if="mainLoader" />
        <NoRecordSvgVue v-else-if="items.length == 0 && mainLoader == false" />

        <table v-else class="text-sm mt-1">
          <thead>
            <tr class="text-xs text-[#212529] leading-[1.5]">
              <th>TRANSACTION ID</th>
              <th>TRANSACTION TYPE</th>
              <th>BUSINESS NAME</th>
              <th>SERVICE</th>
              <th>EXTERNAL ID</th>
              <th>CURRENCY</th>
              <th>AMOUNT</th>
              <th>PHONE NO</th>
              <th>ACCOUNT NO</th>
              <th>STATUS</th>
              <th>MESSAGE</th>
              <th>DATE</th>
              <th>ACTION</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="data in items" :key="data.id">
              <td data-label="TRANSACTION ID">
                {{ data.transaction_id ?? '-' }}
              </td>
              <td data-label="TRANSACTION TYPE">
                {{ data.transaction_type ?? '-' }}
              </td>
              <td data-label="BUSINESS NAME">
                {{ data.client.business_name ?? '-' }}
              </td>
              <td data-label="SERVICE">
                {{ serviceCodeToName(data.service_id) ?? '-' }}
              </td>
              <td data-label="EXTERNAL ID">
                {{ data.external_id ?? '-' }}
              </td>
              <td data-label="CURRENCY">
                {{ data.currency ?? '-' }}
              </td>
              <td data-label="AMOUNT">
                {{ data.amount ?? '-' }}
              </td>
              <td data-label="PHONE NO.">
                {{ data.phone_number ?? '-' }}
              </td>
              <td data-label="ACCOUNT NO.">
                {{ data.account_number ?? '-' }}
              </td>
              <td data-label="STATUS">
                {{ statusCodeToName(data.status_code) }}
              </td>
              <td data-label="MESSAGE">
                {{ data.response_message ?? '-' }}
              </td>
              <td data-label="DATE">
                {{ new Date(data.created_at).toLocaleString() }}
              </td>
              <td class="before:hidden lg:w-1 whitespace-nowrap">
                <BaseButtons type="justify-start lg:justify-end" no-wrap>
                  <BaseButton
                    @click="viewTransaction(data?.id)"
                    :icon="mdiEyeCircleOutline"
                    label="View"
                    color="contrast"
                    outline
                    small
                  />
                </BaseButtons>
              </td>
            </tr>
          </tbody>
        </table>
      </CardBox>
      <div
        class="p-3 lg:px-6 border-t border-gray-100 dark:border-slate-800"
        v-if="!mainLoader && transactionStore.total_pages > 1"
      >
        <Pagination
          v-model="currentPage"
          :total-items="transactionStore.total_count"
          :items-per-page="transactionStore.limit"
        />
      </div>
      <Loading v-model="loading" />
    </SectionMainFull>
  </LayoutAuthenticated>
</template>
<script setup>
import {
  onMounted,
  onBeforeMount,
  computed,
  ref,
  toRefs,
  defineProps,
  watchEffect,
  watch
} from 'vue'
import { useSuccess } from '@/helpers/functions/notifications.js'
import axios from 'axios'
import { useRouter, useRoute } from 'vue-router'
import FormControl from '@/components/FormControl.vue'
import FormField from '@/components/FormField.vue'
import BreadcrumbBase from '@/components/Reusables/Breadcrumbs/BreadcrumbBase.vue'
import BreadcrumbContinue from '@/components/Reusables/Breadcrumbs/BreadcrumbContinue.vue'
import BreadcrumbEnd from '@/components/Reusables/Breadcrumbs/BreadcrumbEnd.vue'
import DatabaseLoaderOne from '@/components/Reusables/Loaders/DatabaseLoaderOne.vue'
import SpinnerOne from '@/components/Reusables/Loaders/SpinnerOne.vue'
import NoRecordSvgVue from '@/components/Reusables/Svgs/NoRecordSvg.vue'
import { useAuthStore } from '@/stores/auth.js'
import { useTransactionStore } from '@/stores/transactions.js'
import {
  mdiFileExcelBoxOutline,
  mdiEyeCircleOutline,
  mdiReload,
  mdiFilterOutline,
  mdiCash,
  mdiExport
} from '@mdi/js'
import { statusCodeToName, serviceCodeToName } from '@/helpers/functions/status.js'
import SectionMainFull from '@/components/SectionMainFull.vue'
import CardBox from '@/components/CardBox.vue'
import LayoutAuthenticated from '@/layouts/LayoutAuthenticated.vue'
import BaseButton from '@/components/BaseButton.vue'
import CardBoxModal from '@/components/CardBoxModal.vue'
import BaseIcon from '@/components/BaseIcon.vue'
import BaseButtons from '@/components/BaseButtons.vue'

import * as chartConfig from '@/components/Charts/chart.config.js'
import CardBoxWidget from '@/components/CardBoxWidget.vue'
import Pagination from '@/components/Pagination.vue'
import { endOfToday, format, startOfToday } from 'date-fns'
import Loading from '@/components/modals/Loading.vue'
const chartData = ref(null)

const fillChartData = () => {
  chartData.value = chartConfig.sampleChartData()
}

const transactionStore = useTransactionStore()
const authStore = useAuthStore()

onMounted(async () => {
  try {
    mainLoader.value = true
    await fillChartData()
    await transactionStore.getTransactions(1)
  } catch (e) {
  } finally {
    mainLoader.value = false
  }
})

const items = computed(() => {
  return Array.isArray(transactionStore.transactions) ? transactionStore.transactions : []
})

const isFilterModalActive = ref(false)
const isViewTransactionActive = ref(false)
const currentPage = ref(1)
const checkedRows = ref([])
const checkedDeleteRows = ref([])
const selectedViewData = ref([])
const searchValue = ref('')
const mainLoader = ref(false)
const reloaded = ref(false)
const loading = ref(false)
const push = useSuccess()
const route = useRoute()
const router = useRouter()
const statusOptions = [
  { id: '', label: 'All Payments' },
  { id: '300', label: 'Success' },
  { id: '301', label: 'Failed' },
  { id: '304', label: 'Refunds' },
  { id: '303', label: 'Ambiguous' },
  { id: '283', label: 'Pending' }
]
const serviceOptions = [
  { id: '', label: 'All Services' },
  { id: '1', label: 'Airtel Money' },
  { id: '2', label: 'MTN Money' },
  { id: '3', label: 'Zamtel Money' },
  { id: '4', label: 'Airtel Airtime' },
  { id: '5', label: 'MTN Airtime' },
  { id: '6', label: 'Zamtel Airtime' },
  { id: '7', label: 'Card' },
  { id: '8', label: 'Zesco' },
  { id: '9', label: 'Momo Test' },
  { id: '10', label: 'Airtime Test' }
]
const transactionTypeOptions = [
  { id: '', label: 'All Transactions' },
  { id: 'COLLECTION', label: 'Collection' },
  { id: 'DISBURSEMENT', label: 'Disbursement' }
]

const currencyOptions = [
  { id: '', label: 'All Currencies' },
  { id: 'USD', label: 'USD' },
  { id: 'ZMW', label: 'ZMW' }
]
const filterData = ref({
  init_start_date: format(startOfToday(), "yyyy-MM-dd'T'HH:mm"),
  init_end_date: format(endOfToday(), "yyyy-MM-dd'T'HH:mm"),
  start_date: '',
  start_time: '',
  end_date: '',
  end_time: '',
  business_name: '',
  status: statusOptions[0],
  service: serviceOptions[0],
  currency: currencyOptions[0],
  transaction_type: transactionTypeOptions[0],
  phone_number: '',
  account_number: '',
  transaction_id: '',
  external_id: '',
  narration: ''
})

const splitDateTimeLocal = async () => {
  const [start_date, start_time] = filterData.value.init_start_date.split('T')
  const [start_hours, start_minutes] = start_time.split(':')

  filterData.value.start_date = start_date
  filterData.value.start_time = `${start_hours}:${start_minutes}`
  const [end_date, end_time] = filterData.value.init_end_date.split('T')
  const [end_hours, end_minutes] = end_time.split(':')

  filterData.value.end_date = end_date
  filterData.value.end_time = `${end_hours}:${end_minutes}`
}

const totalAmount = computed(() => {
  return items.value.reduce((total, item) => {
    return total + parseFloat(item.amount)
  }, 0)
})
const totalCount = computed(() => {
  return items.value.length
})
const failedFilteredItems = computed(() => {
  return items.value.filter((item) => item.status_code === 301)
})

const failedTotalAmount = computed(() => {
  return failedFilteredItems.value.reduce((total, item) => {
    return total + parseFloat(item.amount)
  }, 0)
})
const successFilteredItems = computed(() => {
  return items.value.filter((item) => item.status_code === 300)
})

const successTotalAmount = computed(() => {
  return successFilteredItems.value.reduce((total, item) => {
    return total + parseFloat(item.amount)
  }, 0)
})
const pendingFilteredItems = computed(() => {
  return items.value.filter((item) => item.status_code === 283)
})

const pendingTotalAmount = computed(() => {
  return pendingFilteredItems.value.reduce((total, item) => {
    return total + parseFloat(item.amount)
  }, 0)
})

const accountData = computed(() => {
  return authStore.accountData
})
const companyData = computed(() => {
  return authStore.companyData
})

const dateRangeText = computed(() => {
  if (filterData.value.init_start_date && filterData.value.init_end_date) {
    const startDate = new Date(filterData.value.init_start_date)
    const endDate = new Date(filterData.value.init_end_date)

    const formatDate = (date) => {
      const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']
      const months = [
        'Jan',
        'Feb',
        'Mar',
        'Apr',
        'May',
        'Jun',
        'Jul',
        'Aug',
        'Sep',
        'Oct',
        'Nov',
        'Dec'
      ]

      const dayName = days[date.getDay()]
      const day = date.getDate()
      const month = months[date.getMonth()]
      const year = date.getFullYear()
      const hours = date.getHours().toString().padStart(2, '0')
      const minutes = date.getMinutes().toString().padStart(2, '0')

      return `${dayName} ${day} ${month}, ${year} ${hours}:${minutes}`
    }

    return `Showing payments for ${formatDate(startDate)} to ${formatDate(endDate)}`
  }
  return 'Showing all payments'
})

const startFilterModal = (status) => {
  isFilterModalActive.value = status
}

const viewTransaction = (transactionId) => {
  router.push({ name: 'transaction-detail', params: { id: transactionId } })
}

watch(currentPage, async () => {
  if (filterData.value.start_date) {
    await filterDataFn()
  } else {
    try {
      if (reloaded.value) {
        reloaded.value = false
        return
      }

      mainLoader.value = true
      await transactionStore.getTransactions(currentPage.value)
    } catch (e) {
    } finally {
      mainLoader.value = false
    }
  }
})

const reloadData = async () => {
  try {
    mainLoader.value = true
    await transactionStore.getTransactions(1)
    reloaded.value = true
    currentPage.value = 1
  } catch (e) {
  } finally {
    mainLoader.value = false
    clearstates()
  }
}

const filterDataFn = async () => {
  mainLoader.value = true
  await splitDateTimeLocal()
  try {
    const data = {
      from_date: filterData.value.start_date,
      from_date_time: filterData.value.start_time,
      to_date: filterData.value.end_date,
      to_date_time: filterData.value.end_time,
      status_code: filterData.value.status.id,
      transaction_id: filterData.value.transaction_id,
      external_id: filterData.value.external_id,
      transaction_type: filterData.value.transaction_type.id,
      service_id: filterData.value.service.id,
      currency: filterData.value.currency.id,
      phone_number: filterData.value.phone_number,
      account_number: filterData.value.account_number,
      narration: filterData.value.narration,
      page: currentPage.value
    }

    let res = await transactionStore.getFilteredTransactions(data)
    if (res == 200) {
    }
  } catch (error) {
  } finally {
    mainLoader.value = false
  }
}

const exportTransactions = async () => {
  try {
    loading.value = true

    const data = {
      from_date: filterData.value.start_date,
      from_date_time: filterData.value.start_time,
      to_date: filterData.value.end_date,
      to_date_time: filterData.value.end_time,
      status_code: filterData.value.status.id,
      transaction_id: filterData.value.transaction_id,
      external_id: filterData.value.external_id,
      transaction_type: filterData.value.transaction_type.id,
      service_id: filterData.value.service.id,
      phone_number: filterData.value.phone_number,
      account_number: filterData.value.account_number,
      narration: filterData.value.narration
    }

    const response = await transactionStore.exportTransactions(data)

    if (response.status != 200) {
      return
    }

    const url = window.URL.createObjectURL(new Blob([response.data]))
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', 'transactions.xlsx')
    document.body.appendChild(link)
    link.click()
    link.remove()
  } catch (error) {
  } finally {
    loading.value = false
  }
}

const clearstates = () => {
  filterData.value = {
    init_start_date: format(startOfToday(), "yyyy-MM-dd'T'HH:mm"),
    init_end_date: format(endOfToday(), "yyyy-MM-dd'T'HH:mm"),
    start_date: '',
    start_time: '',
    end_date: '',
    end_time: '',
    business_name: '',
    status: statusOptions[0],
    service: serviceOptions[0],
    currency: currencyOptions[0],
    transaction_type: transactionTypeOptions[0],
    phone_number: '',
    account_number: '',
    transaction_id: '',
    external_id: '',
    narration: ''
  }
}

const json_fields = {
  Organization: {
    field: 'service_id',
    callback: (value) => {
      return companyData.value.name
    }
  },
  'Business Name': {
    field: 'service_id',
    callback: (value) => {
      return accountData.value.business_name
    }
  },
  'Transaction ID': 'transaction_id',
  'Transaction Type': 'transaction_type',
  Amount: 'amount',
  'Account Number': 'account_number',
  Narration: 'narration',
  phone_number: 'phone_number',
  Service: {
    field: 'service_id',
    callback: (value) => {
      return serviceCodeToName(value)
    }
  },
  'External ID': 'external_id',
  Status: {
    field: 'status_code',
    callback: (value) => {
      return statusCodeToName(value)
    }
  },
  'Merchant ID': 'merchant_id',
  Currency: 'currency',
  Message: 'response_message',
  'Response Code': 'response_code',
  Date: 'created_at'
}

const json_data = computed(() => {
  return items.value
})

const json_meta = [
  [
    {
      key: 'charset',
      value: 'utf-8'
    }
  ]
]

const jsonExcel = ref(null)

const exportToExcel = () => {
  if (jsonExcel.value) {
    jsonExcel.value.generateData()
  }
}
</script>
