{"name": "next-payment-gateway", "version": "3.3.0", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview --port 4173", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@headlessui/vue": "^1.7.15", "@mdi/js": "^7.0.96", "@rushstack/eslint-patch": "^1.1.4", "@vuepic/vue-datepicker": "^9.0.2", "@vueuse/core": "^10.3.0", "axios": "^1.5.0", "chart.js": "^4.4.0", "date-fns": "^3.6.0", "dayjs": "^1.11.11", "epic-spinners": "^2.0.0", "moment": "^2.30.1", "notivue": "^1.4.5", "numeral": "^2.0.6", "pinia": "^2.1.6", "pinia-plugin-persist": "^1.0.0", "sleep-promise": "^9.1.0", "swiper": "^10.2.0", "vue": "^3.3.4", "vue-awesome-paginate": "^1.2.0", "vue-data-ui": "^1.5.4", "vue-json-excel3": "^1.0.1", "vue-multiselect": "^3.0.0-beta.3", "vue-pdf-embed": "^2.0.2", "vue-router": "^4.2.4", "vue3-easy-data-table": "^1.5.47", "vue3-text-clamp": "^0.1.2"}, "devDependencies": {"@rushstack/eslint-patch": "^1.3.3", "@tailwindcss/forms": "^0.5.6", "@vitejs/plugin-vue": "^4.3.4", "@vue/eslint-config-prettier": "^8.0.0", "autoprefixer": "^10.4.15", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "postcss": "^8.4.30", "postcss-import": "^15.1.0", "prettier": "^3.0.3", "tailwindcss": "^3.3.3", "vite": "^4.4.9"}}