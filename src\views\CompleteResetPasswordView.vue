<template>
  <!-- This is an example component -->
<div class="h-screen font-sans login bg-cover">
<div class="container mx-auto h-full flex flex-1 justify-center items-center">
    <div class="w-full max-w-lg">
      <div class="leading-loose">
        <form class="max-w-sm m-4 p-10 bg-white bg-opacity-25 rounded shadow-xl" style="background-color: #ffffffb0;">
          <div class="flex justify-center items-center">
          <img :src="Logo" style="height: 60px;"/>
        </div>
        <h1 class="text-2xl font-semibold mb-4"></h1>
            <p class="text-xl font-semibold mb-4 text-center">ENTER NEW PASSWORD</p>
            <div class="mb-4">
            <FormField label="">
              <FormControl v-model="form.password" placeholder="Password" type="password" :icon="mdiLockOutline" />
            </FormField>
          </div>
          <div class="mb-4">
            <FormField label="">
              <FormControl v-model="form.confirmPassword" placeholder="Confirm Password" type="password" :icon="mdiLockOutline" />
            </FormField>
          </div>

              <div class="mt-4 items-center flex justify-between">
                <BaseButton :disabled="!validateSubmitState" color="info" label="UPDATE" class="w-full" @click="submit()" />
              </div>
              <div class="text-center">
                <div>
                <router-link to="/login" class="inline-block right-0 align-baseline text-sm text-500 text-black hover:text-gray-500"
                  href="#">Already have an account</router-link>
                </div>
              </div>

                 <div v-if="!validateSubmitState" class="border bg-gray-50 p-3 rounded text-red-400">
                    <div v-if="!passwordRequired" class="flex space-x-2">
                        <BaseIcon :path="mdiAlertCircleOutline" size="20" h="20" w="20" />
                        <div>Password is required</div>
                    </div>
                    <div v-if="!eightCharacters" class="flex space-x-2">
                        <BaseIcon :path="mdiAlertCircleOutline" size="20" h="20" w="20" />
                        <div>Must be at least 8 characters.</div>
                    </div>
                    <div v-if="!upperLetters" class="flex space-x-2">
                        <BaseIcon :path="mdiAlertCircleOutline" size="20" h="20" w="20" />
                        <div>Must contain at least one uppercase letter.</div>
                    </div>
                    <div v-if="!lowerLetters" class="flex space-x-2">
                        <BaseIcon :path="mdiAlertCircleOutline" size="20" h="20" w="20" />
                        <div>Must contain at least one lowercase letter.</div>
                    </div>
                    <div v-if="!oneNumber" class="flex space-x-2">
                        <BaseIcon :path="mdiAlertCircleOutline" size="20" h="20" w="20" />
                        <div>Must contain at least one number.</div>
                    </div>
                    <div v-if="!oneSymbol" class="flex space-x-2">
                        <BaseIcon :path="mdiAlertCircleOutline" size="20" h="20" w="20" />
                        <div>Must contain at least one symbol.</div>
                    </div>
                    <div v-if="noSpace" class="flex space-x-2">
                        <BaseIcon :path="mdiAlertCircleOutline" size="20" h="20" w="20" />
                        <div>Must not contain space.</div>
                    </div>
                    <div v-if="!passwordMatch" class="flex space-x-2">
                        <BaseIcon :path="mdiAlertCircleOutline" size="20" h="20" w="20" />
                        <div>Passwords should match.</div>
                    </div>
                </div>
                <div v-else class="border-green-500 bg-green-50 p-3 rounded text-green-400">
                    <div class="flex space-x-2">
                        <BaseIcon :path="mdiCheckCircleOutline" size="20" h="20" w="20" />
                        <div>Password is secure</div>
                    </div>
                </div>
        </form>

      </div>
    </div>
  </div>
</div>
</template>
<script setup>
import { reactive, ref, computed } from 'vue'
import { useRouter, useRoute  } from 'vue-router'
import { mdiLockOutline, mdiEmailOutline, mdiShieldLockOutline, mdiAlertCircleOutline, mdiCheckCircleOutline } from '@mdi/js'
import SectionFullScreen from '@/components/SectionFullScreen.vue'
import CardBox from '@/components/CardBox.vue'
import FormCheckRadio from '@/components/FormCheckRadio.vue'
import FormField from '@/components/FormField.vue'
import FormControl from '@/components/FormControl.vue'
import BaseButton from '@/components/BaseButton.vue'
import BaseButtons from '@/components/BaseButtons.vue'
import LayoutGuest from '@/layouts/LayoutGuest.vue'
import BaseIcon from "@/components/BaseIcon.vue";
import Logo from "@/assets/images/logos/logo.png";
import Background from "@/assets/images/backgrounds/bg-1.jpg";
import { useAuthStore } from "@/stores/auth.js";
import { useInfo, useSuccess, useWarning } from '@/helpers/functions/notifications.js';
const infoPush = useInfo();
const successPush = useSuccess();
const warningPush = useWarning();
const auth = useAuthStore();
const loading = ref(false);
const otpStep = ref(false);
const form = reactive({
  password: '',
  confirmPassword: '',
  remember: true,
  otp: ''
})

const router = useRouter()
const route = useRoute();

const submit = async () => {
  loading.value = true;
  try {
    let rs = await auth.resetPassword(form.password,route.query.token);
    if (rs == 200) {
      successPush('Password updated', "You'll be redirected to the Login page");
      setTimeout(() => {
        router.push('/login');
      }, 3000);
    } else if (rs == 400) {
      warningPush('Failed to Proceed', 'Invalid Token.');
    }
  } catch (error) {
    warningPush('Something went wrong', 'Please try again.');
  } finally {
    loading.value = false;
  };
};

const submitOtp = async () => {
  loading.value = true;
  try {
    let rs = await auth.onSubmitOtp(form.otp.toString());
    if (rs == 200) {
      successPush('Successfully logged in', '');
      router.push('/company-hub');
    } else if (rs == 400) {
      warningPush('Failed to Proceed', 'Check your OTP.');
    }
  } catch (error) {
    warningPush('Something went wrong', 'Please try again.');
  } finally {
    loading.value = false;
  };
};
const register = () => {
  router.push('/register')
}
const goBack = () => {
  clearstates();
}
const clearstates = () => {
  form.confirmPassword = "";
  form.password = "";
}

const passwordRequired = computed(() => {
    if (form.password) {
        return true;
    } else {
        return false;
    }
});
const eightCharacters = computed(() => {
    if (form.password.length >= 8) {
        return true;
    } else {
        return false;
    }
});
const upperLetters = computed(() => {
    if (/[A-Z]/.test(form.password)) {
        return true;
    } else {
        return false;
    }
});
const lowerLetters = computed(() => {
    if (/[a-z]/.test(form.password)) {
        return true;
    } else {
        return false;
    }
});
const oneNumber = computed(() => {
    if (/[0-9]/.test(form.password)) {
        return true;
    } else {
        return false;
    }
});
const oneSymbol = computed(() => {
    if (/[^A-Za-z0-9]/.test(form.password)) {
        return true;
    } else {
        return false;
    }
});
const passwordMatch = computed(() => {
    if (form.password.length > 0 && form.password.trim() === form.confirmPassword.trim()) {
        return true;
    } else {
        return false;
    }
});
const noSpace = computed(() => {
    if (/\s/.test(form.password)) {
        return true;
    } else {
        return false;
    }
});
const validateSubmitState = computed(() => {
    if (passwordRequired.value && eightCharacters.value && upperLetters.value && lowerLetters.value && oneNumber.value && oneSymbol.value && passwordMatch.value && !noSpace.value) {
        return true;
    } else {
        return false;
    }
})
</script>
<style scoped>
  .login{
  background: url('@/assets/images/backgrounds/bg-1.jpg');
  background-repeat: no-repeat;
  background-size: cover;
}
</style>
