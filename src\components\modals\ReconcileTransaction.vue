<template>
  <CardBoxModal
    v-model="model"
    title="Reconcile Transaction"
    button="info"
    button-label=""
    action-label="confirm"
  >
    <div class="grid grid-cols-1 lg:grid-cols-1">
      <FormField label="Status">
        <div>
          <FormControl
            v-model="form.status_code"
            type="select"
            name="status"
            :options="transactionStatuses"
          />
        </div>
      </FormField>
      <FormField label="Responce Message">
        <div>
          <FormControl
            v-model="form.response_message"
            type="tel"
            name="response_message"
            placeholder="e.g Transaction was refunded"
          />
        </div>
      </FormField>
    </div>
    <div class="flex mt-3">
      <BaseButton label="Reconcile" color="info" outline @click="reconcileTransaction" />
    </div>
  </CardBoxModal>
  <Loading v-model="isLoading" />
</template>

<script setup>
import { reactive, ref } from 'vue'
import sleep from 'sleep-promise'
import BaseButton from '../BaseButton.vue'
import CardBoxModal from '../CardBoxModal.vue'
import FormField from '../FormField.vue'
import FormControl from '../FormControl.vue'
import { useTransactionStore } from '@/stores/transactions'
import { usePush } from 'notivue'
import Loading from './Loading.vue'

const props = defineProps(['transaction'])

const form = reactive({
  status_code: {
    label: '-- Select Status --',
    id: '0'
  },
  response_message: ''
})

const model = defineModel()
const isLoading = ref(false)
const push = usePush()
const transactionStore = useTransactionStore()

async function reconcileTransaction() {
  if (!form.status_code || !form.response_message || form.status_code == 0) {
    push.error({ message: 'Kindly fill out all the fields', duration: 1000 })
    return
  }

  isLoading.value = true
  // await sleep(3000)
  const status = await transactionStore.reconcileTransaction({
    status_code: form.status_code.id,
    response_message: form.response_message,
    external_id: props.transaction.external_id
  })
  isLoading.value = false

  if (status != 200) {
    if (status == 404) {
      push.error({ message: 'Transaction not found', duration: 1000 })
    } else {
      push.error({ message: 'Something went wrong', duration: 1000 })
    }
  } else {
    push.success({ message: 'Transaction reconciled', duration: 1000 })
  }
}

const transactionStatuses = [
  {
    label: '-- Select Status --',
    id: '0'
  },
  {
    label: 'Awaiting Payment Confirmation',
    id: '283'
  },
  {
    label: 'Successful',
    id: '300'
  },
  {
    label: 'Un-Successful',
    id: '301'
  },
  {
    label: 'Refunded',
    id: '304'
  },
  {
    label: 'Ambigous',
    id: '303'
  }
]
</script>

<style lang="scss" scoped></style>
