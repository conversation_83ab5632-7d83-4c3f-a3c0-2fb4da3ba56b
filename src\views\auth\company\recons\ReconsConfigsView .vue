<template>
  <LayoutAuthenticated>
    <SectionMainFull>
      <SectionTitleLineWithButton :icon="mdiCashClock" title="Configs" small>
      </SectionTitleLineWithButton>

      <!-- Add Config Modal -->
      <CardBoxModal
        v-model="isAddConfigModalActive"
        title="ADD CONFIG"
        button="info"
        button-label=""
        action-label="confirm"
      >
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
          <!-- Client Section -->
          <FormField label="Client">
            <FormControl
              v-model="configForm.client"
              :options="clientOptions"
              placeholder="Select a client"
              required
            />
          </FormField>

          <!-- Account Section -->
          <FormField label="Account Number">
            <FormControl
              v-model="configForm.account_number"
              type="text"
              placeholder="Enter Account Number"
              required
            />
          </FormField>

          <FormField label="Account Name">
            <FormControl
              v-model="configForm.account_name"
              type="text"
              placeholder="Enter Account Name"
              required
            />
          </FormField>

          <!-- Commission Rates -->
          <FormField label="Momo Commission Rates (%)">
            <FormControl
              v-model="configForm.momo_comm_rates"
              type="number"
              step="0.01"
              placeholder="Enter Momo Commission Rate"
              required
            />
          </FormField>

          <FormField label="Card Commission Rates (%)">
            <FormControl
              v-model="configForm.card_comm_rates"
              type="number"
              step="0.01"
              placeholder="Enter Card Commission Rate"
              required
            />
          </FormField>

          <FormField label="Bank Commission Rates (%)">
            <FormControl
              v-model="configForm.bank_commission"
              type="number"
              step="0.01"
              placeholder="Enter Bank Commission Rate"
              required
            />
          </FormField>

          <!-- Revenue Share -->
          <FormField label="Revenue Share">
            <FormControl
              v-model="configForm.revenue_share"
              :options="revenueShareOptions"
              placeholder="Enter Revenue Share"
              required
            />
          </FormField>

          <!-- Client Share -->
          <FormField label="PrimeNet Share (%)">
            <FormControl
              v-model="configForm.primenet_revenue_share"
              type="number"
              step="0.01"
              placeholder="Enter PrimeNet Share"
              required
            />
          </FormField>

          <!-- Settlement Model -->
          <FormField label="Settlement Model">
            <FormControl
              v-model="configForm.model"
              type="text"
              placeholder="Enter Settlement Model"
              required
            />
          </FormField>

          <!-- Email -->
          <FormField label="Email">
            <FormControl
              v-model="configForm.email"
              type="email"
              placeholder="Enter Email"
              required
            />
          </FormField>
        </div>

        <BaseButton
          label="Save Config"
          color="info"
          @click="addConfigFn()"
          :disabled="loading"
          :loading="loading"
        />
      </CardBoxModal>

      <!-- Edit Config Modal -->
      <CardBoxModal
        v-model="isEditConfigModalActive"
        title="EDIT CONFIG"
        button="info"
        button-label=""
        action-label="confirm"
      >
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
          <!-- Client Section -->
          <FormField label="Client">
            <FormControl
              v-model="editForm.client"
              :options="clientOptions"
              placeholder="Select a client"
              required
            />
          </FormField>

          <!-- Account Section -->
          <FormField label="Account Number">
            <FormControl
              v-model="editForm.account_number"
              type="text"
              placeholder="Enter Account Number"
              required
            />
          </FormField>

          <FormField label="Account Name">
            <FormControl
              v-model="editForm.account_name"
              type="text"
              placeholder="Enter Account Name"
              required
            />
          </FormField>

          <!-- Commission Rates -->
          <FormField label="Momo Commission Rates (%)">
            <FormControl
              v-model="editForm.momo_comm_rates"
              type="number"
              step="0.01"
              placeholder="Enter Momo Commission Rate"
              required
            />
          </FormField>

          <FormField label="Card Commission Rates (%)">
            <FormControl
              v-model="editForm.card_comm_rates"
              type="number"
              step="0.01"
              placeholder="Enter Card Commission Rate"
              required
            />
          </FormField>

          <FormField label="Bank Commission Rates (%)">
            <FormControl
              v-model="editForm.bank_commission"
              type="number"
              step="0.01"
              placeholder="Enter Bank Commission Rate"
              required
            />
          </FormField>

          <!-- Revenue Share -->
          <FormField label="Revenue Share">
            <FormControl
              v-model="editForm.revenue_share"
              :options="revenueShareOptions"
              placeholder="Enter Revenue Share"
              required
            />
          </FormField>

          <!-- Client Share -->
          <FormField label="PrimeNet Share (%)">
            <FormControl
              v-model="editForm.primenet_revenue_share"
              type="number"
              step="0.01"
              placeholder="Enter PrimeNet Share"
              required
            />
          </FormField>

          <!-- Settlement Model -->
          <FormField label="Settlement Model">
            <FormControl
              v-model="editForm.model"
              type="text"
              placeholder="Enter Settlement Model"
              required
            />
          </FormField>

          <!-- Email -->
          <FormField label="Email">
            <FormControl v-model="editForm.email" type="email" placeholder="Enter Email" required />
          </FormField>
        </div>

        <BaseButton
          label="Update Config"
          color="info"
          @click="updateConfigFn"
          :disabled="loading"
          :loading="loading"
        />
      </CardBoxModal>

      <!-- Main Table Section -->
      <CardBox rounded="rounded-sm" class="mb-6 px-2" has-table>
        <div class="flex py-2 justify-between items-center">
          <div class="flex justify-items-end items-center space-x-5">
            <FormControl v-model="searchValue" type="search" name="search" placeholder="Search" />
            <BaseIcon
              @click="reloadData()"
              :path="mdiReload"
              size="30"
              w=""
              h="h-12"
              class="text-gray-600 p-1 h-full border border-black rounded cursor-pointer hover:bg-black hover:text-white"
              title="Reload"
            />
          </div>
          <div>
            <BaseButton
              @click="isAddConfigModalActive = true"
              :icon="mdiPlus"
              label="Config"
              color="info"
            />
          </div>
        </div>

        <DatabaseLoaderOne v-if="items.length == 0 && mainLoader" />
        <NoRecordSvgVue v-else-if="items.length == 0 && mainLoader == false" />

        <CardBox rounded="rounded-sm" class="mb-6 px-2" has-table style="overflow: auto">
          <table v-if="items.length > 0" class="text-sm mt-1">
            <thead>
              <tr class="text-xs text-[#212529] leading-[1.5]">
                <th>CLIENT</th>
                <th>ACCOUNT NUMBER</th>
                <th>ACCOUNT NAME</th>
                <th>MOMO COMM RATES</th>
                <th>CARD COMM RATES</th>
                <th>BANK COMM RATES</th>
                <th>REVENUE SHARE</th>
                <th>PRIMENET SHARE</th>
                <th>SETTLEMENT MODEL</th>
                <th>EMAIL</th>
                <th>CREATED</th>
                <th>ACTION</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="data in itemsPaginated" :key="data.id">
                <td data-label="CLIENT">
                  {{ data.client?.business_name ?? '-' }}
                </td>
                <td data-label="ACCOUNT NUMBER">
                  {{ data.account_number ?? '-' }}
                </td>
                <td data-label="ACCOUNT NAME">
                  {{ data.account_name ?? '-' }}
                </td>
                <td data-label="MOMO COMM RATES">
                  {{ data.momo_comm_rates ?? '-' }}
                </td>
                <td data-label="CARD COMM RATES">
                  {{ data.card_comm_rates ?? '-' }}
                </td>
                <td data-label="BANK COMM RATES">
                  {{ data.bank_commission ?? '-' }}
                </td>
                <td data-label="REVENUE SHARE">
                  {{ data.revenue_share ? 'Yes' : 'No' }}
                </td>
                <td data-label="PRIMENET SHARE">
                  {{ data.primenet_revenue_share ?? '-' }}
                </td>
                <td data-label="SETTLEMENT MODEL">
                  {{ data.model ?? '-' }}
                </td>
                <td data-label="EMAIL">
                  {{ data.email ?? '-' }}
                </td>
                <td data-label="CREATED">
                  {{ new Date(data.created_at).toLocaleString() }}
                </td>
                <td class="before:hidden lg:w-1 whitespace-nowrap">
                  <BaseButtons type="justify-start lg:justify-end" no-wrap>
                    <BaseButton
                      @click="openEditModal(data)"
                      :icon="mdiPencil"
                      label="Edit"
                      color="info"
                      outline
                      small
                    />
                  </BaseButtons>
                </td>
              </tr>
            </tbody>
          </table>
        </CardBox>

        <div
          class="p-3 lg:px-6 border-t border-gray-100 dark:border-slate-800"
          v-if="!mainLoader && items.length > 0 && numPages > 1"
        >
          <Pagination
            v-model="currentPage"
            :total-items="items.length"
            :per-page="perPage"
            :total-pages="numPages"
            @page-changed="handlePageChange"
          />
        </div>
      </CardBox>
    </SectionMainFull>
  </LayoutAuthenticated>
</template>

<script setup>
import BaseButton from '@/components/BaseButton.vue'
import BaseButtons from '@/components/BaseButtons.vue'
import BaseIcon from '@/components/BaseIcon.vue'
import BaseLevel from '@/components/BaseLevel.vue'
import CardBox from '@/components/CardBox.vue'
import CardBoxModal from '@/components/CardBoxModal.vue'
import FormControl from '@/components/FormControl.vue'
import FormField from '@/components/FormField.vue'
import DatabaseLoaderOne from '@/components/Reusables/Loaders/DatabaseLoaderOne.vue'
import NoRecordSvgVue from '@/components/Reusables/Svgs/NoRecordSvg.vue'
import FormErrorMessage from '@/components/FormErrorMessage.vue'
import Pagination from '@/components/Pagination.vue'
import SectionMainFull from '@/components/SectionMainFull.vue'
import SectionTitleLineWithButton from '@/components/SectionTitleLineWithButton.vue'
import { useInfo, useSuccess, useWarning } from '@/helpers/functions/notifications.js'
import { useRoute, useRouter } from 'vue-router'
import LayoutAuthenticated from '@/layouts/LayoutAuthenticated.vue'
import { useAuthStore } from '@/stores/auth.js'
import { useReconsStore } from '@/stores/recons.js'
import { mdiCashClock, mdiReload, mdiPlus, mdiPencil } from '@mdi/js'
import { computed, defineProps, onMounted, ref, watch } from 'vue'

const router = useRouter()
const warningPush = useWarning()
const successPush = useSuccess()
const authStore = useAuthStore()
const reconStore = useReconsStore()
const isAddConfigModalActive = ref(false)
const isEditConfigModalActive = ref(false)
const currentPage = ref(1)
const searchValue = ref('')
const mainLoader = ref(false)
const loading = ref(false)

const revenueShareOptions = [
  { id: '1', label: 'Yes' },
  { id: '0', label: 'No' }
]

// Config form data
const configForm = ref({
  client: null,
  account_number: '',
  account_name: '',
  momo_comm_rates: '',
  card_comm_rates: '',
  bank_commission: '',
  revenue_share: { id: '0', label: 'No' },
  primenet_revenue_share: '',
  model: '',
  email: ''
})

// Edit form data
const editForm = ref({
  id: '',
  client: null,
  account_number: '',
  account_name: '',
  momo_comm_rates: '',
  card_comm_rates: '',
  bank_commission: '',
  revenue_share: '',
  primenet_revenue_share: '',
  model: '',
  email: ''
})

onMounted(async () => {
  try {
    mainLoader.value = true
    await reconStore.getConfigs(authStore.companyData.id, currentPage.value)
  } catch (e) {
  } finally {
    mainLoader.value = false
  }
})

const items = computed(() => {
  return Array.isArray(reconStore.configs?.data) ? reconStore.configs.data : []
})

const clientsData = computed(() => {
  return authStore.clientsData || []
})

const clientOptions = computed(() => {
  return clientsData.value.map((client) => ({
    id: client.id,
    label: client.business_name
  }))
})

// Pagination logic
const perPage = ref(10)

const itemsPaginated = computed(() =>
  items.value.slice(perPage.value * (currentPage.value - 1), perPage.value * currentPage.value)
)

const numPages = computed(() => Math.ceil(items.value.length / perPage.value))

const handlePageChange = (page) => {
  currentPage.value = page
}

watch(currentPage, async () => {
  try {
    mainLoader.value = true
    await reconStore.getConfigs(authStore.companyData.id, currentPage.value)
  } catch (e) {
  } finally {
    mainLoader.value = false
  }
})

const reloadData = async () => {
  try {
    mainLoader.value = true
    await reconStore.getConfigs(currentPage.value)
  } catch (e) {
  } finally {
    mainLoader.value = false
  }
}

const openEditModal = (configData) => {
  // Find the client option from clientOptions based on the client_id
  const selectedClient =
    clientOptions.value.find((option) => option.id === configData.client_id) || null

  editForm.value = {
    id: configData.id,
    client: selectedClient,
    account_number: configData.account_number?.toString() ?? '',
    account_name: configData.account_name?.toString() ?? '',
    momo_comm_rates: configData.momo_comm_rates?.split('%')[0],
    card_comm_rates: configData.card_comm_rates?.split('%')[0],
    bank_commission: configData.bank_commission?.split('%')[0],
    revenue_share: configData.revenue_share ? { id: '1', label: 'Yes' } : { id: '0', label: 'No' },
    primenet_revenue_share: configData.primenet_revenue_share?.split('%')[0],
    model: configData.model?.toString() ?? '',
    email: configData.email?.toString() ?? ''
  }
  isEditConfigModalActive.value = true
}

const addConfigFn = async () => {
  loading.value = true
  try {
    const payload = {
      ...configForm.value,
      client_id: configForm.value.client?.id,
      revenue_share: configForm.value.revenue_share.id,
      momo_comm_rates: configForm.value.momo_comm_rates
        ? `${configForm.value.momo_comm_rates}%`
        : '',
      card_comm_rates: configForm.value.card_comm_rates
        ? `${configForm.value.card_comm_rates}%`
        : '',
      bank_commission: configForm.value.bank_commission
        ? `${configForm.value.bank_commission}%`
        : ''
    }

    // Remove the client object from payload since we're sending client_id
    delete payload.client

    const response = await reconStore.addConfig({
      ...Object.fromEntries(
        Object.entries(payload).filter(
          ([_, value]) => value !== '' && value !== null && value !== undefined
        )
      )
    })

    if (response.status === 201) {
      successPush('Config added successfully')
      isAddConfigModalActive.value = false
      await reloadData()
      resetConfigForm()
    } else {
      warningPush('Failed to add config', response.data?.message || 'Please try again')
    }
  } catch (error) {
    warningPush('Error', 'Failed to add config. Please try again.')
  } finally {
    loading.value = false
  }
}

const updateConfigFn = async () => {
  loading.value = true
  try {
    const payload = {
      ...editForm.value,
      client_id: editForm.value.client?.id,
      revenue_share: editForm.value.revenue_share.id,
      momo_comm_rates: editForm.value.momo_comm_rates ? `${editForm.value.momo_comm_rates}%` : '',
      card_comm_rates: editForm.value.card_comm_rates ? `${editForm.value.card_comm_rates}%` : '',
      bank_commission: editForm.value.bank_commission ? `${editForm.value.bank_commission}%` : ''
    }

    // Remove the client object from payload since we're sending client_id
    delete payload.client

    const response = await reconStore.updateConfig(payload, editForm.value.id)

    if (response.status === 200) {
      successPush('Config updated successfully')
      isEditConfigModalActive.value = false
      await reloadData()
    } else {
      warningPush('Failed to update config', response.data?.message || 'Please try again')
    }
  } catch (error) {
    warningPush('Error', 'Failed to update config. Please try again.')
  } finally {
    loading.value = false
  }
}

const resetConfigForm = () => {
  configForm.value = {
    client: null,
    account_number: '',
    account_name: '',
    momo_comm_rates: '',
    card_comm_rates: '',
    bank_commission: '',
    revenue_share: { id: '0', label: 'No' },
    primenet_revenue_share: '',
    model: '',
    email: ''
  }
}
</script>

<style scoped>
/* Scrollable Table Styles */
.table-container {
  max-height: 60vh;
  overflow-y: auto;
  position: relative;
  margin-bottom: 1rem;
}

table {
  width: 100%;
  border-collapse: collapse;
}

thead {
  position: sticky;
  top: 0;
  background-color: white;
  z-index: 10;
}

th {
  padding: 12px 15px;
  text-align: left;
  background-color: #f8f9fa;
  position: sticky;
  top: 0;
}

td {
  padding: 12px 15px;
  border-bottom: 1px solid #e9ecef;
}

tr:hover {
  background-color: #f8f9fa;
}

/* Form Grid Styles */
.grid-cols-1.lg\:grid-cols-2 {
  gap: 1rem;
}

/* Pagination Styles */
.pagination-container {
  display: flex;
  column-gap: 10px;
}

.paginate-buttons {
  height: 40px;
  width: 40px;
  border-radius: 20px;
  cursor: pointer;
  background-color: rgb(242, 242, 242);
  border: 1px solid rgb(217, 217, 217);
  color: black;
}

.paginate-buttons:hover {
  background-color: #d8d8d8;
}

.active-page {
  background-color: #3498db;
  border: 1px solid #3498db;
  color: white;
}

.active-page:hover {
  background-color: #2988c8;
}
</style>
