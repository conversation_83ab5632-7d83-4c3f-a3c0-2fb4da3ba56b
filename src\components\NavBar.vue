<script setup>
import { computed, ref } from 'vue'
import { mdiClose, mdiDotsVertical } from '@mdi/js'
import { containerMaxW } from '@/config.js'
import BaseIcon from '@/components/BaseIcon.vue'
import NavBarMenuList from '@/components/NavBarMenuList.vue'
import NavBarItemPlain from '@/components/NavBarItemPlain.vue'
import { useAuthStore } from '@/stores/auth'
import { logout } from '@/helpers/functions/auth'

defineProps({
  menu: {
    type: Array,
    required: true
  },
  rightContent: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['menu-click'])

const menuClick = (event, item) => {
  emit('menu-click', event, item)

  if (item.label == 'Logout') {
    logout()
  }
}

const isMenuNavBarActive = ref(false)

const authStore = useAuthStore()

const authUserData = computed(() => {
  return authStore.userData
})
</script>

<template>
  <nav
    class="top-0 inset-x-0 fixed bg-gray-50 h-14 z-30 transition-position w-screen shadow lg:w-auto dark:bg-slate-800"
  >
    <!-- Main container with max width for left content -->
    <div class="flex lg:items-stretch" :class="containerMaxW">
      <div class="flex items-center h-14">
        <slot />
      </div>
    </div>

    <!-- Right-aligned content positioned absolutely to far right -->
    <div class="absolute right-0 top-0 flex items-center h-14 pr-4">
      <!-- Right content slot (for buttons like Check Float) -->
      <div class="flex items-center mr-4">
        <slot name="right-content" />
      </div>

      <!-- Mobile menu toggle -->
      <div class="flex-none items-stretch flex h-14 lg:hidden">
        <NavBarItemPlain @click.prevent="isMenuNavBarActive = !isMenuNavBarActive">
          <BaseIcon :path="isMenuNavBarActive ? mdiClose : mdiDotsVertical" size="24" />
        </NavBarItemPlain>
      </div>

      <!-- Profile menu -->
      <div
        class="max-h-screen-menu flex items-center overflow-y-auto lg:overflow-visible absolute w-screen top-14 right-0 bg-gray-50 shadow-lg lg:w-auto lg:flex lg:static lg:shadow-none dark:bg-slate-800"
        :class="[isMenuNavBarActive ? 'block' : 'hidden lg:flex']"
      >
        <span class="text-sm mr-3">{{ authUserData.name }}</span>
        <NavBarMenuList :menu="menu" @menu-click="menuClick" />
      </div>
    </div>
  </nav>
</template>
