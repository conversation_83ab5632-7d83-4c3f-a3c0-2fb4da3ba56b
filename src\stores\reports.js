import { defineStore } from 'pinia'
import { BASE_URL, api } from '@/helpers/api/http-commons.js'
import axios from 'axios'

export const useReportsStore = defineStore('reports', {
  state: () => {
    return {}
  },
  getters: {},
  actions: {
    async getAccountsSummary(data) {
      try {
        var queryStrings = `?start_date=${data.start_date}&end_date=${data.end_date}&page=${data.page}&limit=${data.limit}`

        if (data.account_number) {
          queryStrings += `&account_number=${data.account_number}`
        }

        if (data.client_id) {
          queryStrings += `&client_id=${data.client_id}`
        }

        if (data.service_id) {
          queryStrings += `&service_id=${data.service_id}`
        }

        if (data.arrange_by_account_number) {
          queryStrings += `&arrange_by_account_number=${data.arrange_by_account_number}`
        }

        let config = {
          method: 'get',
          maxBodyLength: Infinity,
          url: BASE_URL + api.REPORTS.BASE_URL + api.REPORTS.ACCOUNTS_SUMMARIES + queryStrings,
          headers: {
            'Content-Type': 'application/json'
          }
        }

        const response = await axios.request(config)

        return response
      } catch (error) {
        return error.response
      }
    }
  }
})
