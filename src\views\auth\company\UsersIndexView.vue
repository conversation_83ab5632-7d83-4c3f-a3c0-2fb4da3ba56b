<template>
  <LayoutAuthenticated>
    <SectionMainFull>
      <SectionTitleLineWithButton :icon="mdiCashClock" title="Users" small>
      </SectionTitleLineWithButton>
      <CardBoxModal
        v-model="isAddUserModalActive"
        title="ADD USER"
        button="info"
        button-label=""
        action-label="confirm"
      >
        <div v-if="addUserStep == 1">
          <div class="flex items-center justify-between text-xl border-t my-2">
            <div>Profile information</div>
            <div>1/3</div>
          </div>
          <div class="grid grid-cols-1 lg:grid-cols-1">
            <FormField label="Fullname">
              <div>
                <FormControl
                  v-model="formData.name"
                  type="text"
                  name="fullname"
                  placeholder="Enter Fullname"
                  @change="inputValidator($event)"
                />
                <FormErrorMessage
                  v-if="validateForm.name === false"
                  message="Full names must exceed 3 characters."
                  color="#dc2626"
                  textSize=""
                />
              </div>
            </FormField>
            <FormField label="Phone">
              <div>
                <FormControl
                  v-model="formData.phone"
                  type="tel"
                  name="phone"
                  placeholder="Enter Phone e.g 26**********"
                  @change="inputValidator($event)"
                />
                <FormErrorMessage
                  v-if="validateForm.phone === false"
                  message="Phone should have 12 numbers"
                  color="#dc2626"
                  textSize=""
                />
              </div>
            </FormField>
            <FormField label="Email">
              <div>
                <FormControl
                  v-model="formData.email"
                  type="email"
                  name="email"
                  placeholder="Enter Email address"
                  @change="inputValidator($event)"
                />
                <FormErrorMessage
                  v-if="validateForm.email === false"
                  message="Enter correct email format"
                  color="#dc2626"
                  textSize=""
                />
              </div>
            </FormField>
          </div>
          <div class="flex mt-3">
            <BaseButton
              label="Next"
              color="info"
              @click="goToAddUserStep(2)"
              :disabled="!goToStepTwo"
              :loading="loading"
              outline
            />
          </div>
        </div>
        <div v-if="addUserStep == 2">
          <div class="flex items-center justify-between text-lg my-2">
            <div>Roles</div>
            <div>2/3</div>
          </div>
          <div class="grid grid-cols-2 gap-2 lg:grid-cols-2">
            <button
              @click="setRoleType('ADMIN')"
              :class="[
                formData.role == 'ADMIN'
                  ? 'flex items-center text-blue-500  bg-blue-100 border-2 border-blue-500'
                  : 'flex items-center border text-gray-600'
              ]"
            >
              <BaseIcon :path="mdiAccountTie" size="30" w="" h="h-12" class="p-1 h-full" />
              <div class="text-lg">Company Admin</div>
            </button>
            <button
              @click="setRoleType('ACCOUNTS')"
              :class="[
                formData.role == 'ACCOUNTS'
                  ? 'flex items-center text-blue-500  bg-blue-100 border-2 border-blue-500'
                  : 'flex items-center border text-gray-600'
              ]"
            >
              <BaseIcon :path="mdiAccount" size="30" w="" h="h-12" class="p-1 h-full" />
              <div>Account Manager</div>
            </button>
          </div>
          <div class="flex justify-between mt-3">
            <BaseButton
              label="Back"
              color="contrast"
              @click="goToAddUserStep(1)"
              :disabled="loading"
              :loading="loading"
              outline
            />
            <BaseButton
              label="Next"
              color="info"
              @click="goToAddUserStep(3)"
              :disabled="formData.role.length == 0"
              :loading="loading"
              outline
            />
          </div>
        </div>
        <div v-if="addUserStep == 3">
          <div class="flex items-center justify-between text-lg my-2">
            <div>Accounts</div>
            <div>3/3</div>
          </div>
          <div class="grid grid-cols-1 gap-2 lg:grid-cols-2 xl:grid-cols-3">
            <button
              v-for="data in clientsData"
              @click="addAccountId(data.id)"
              class="flex space-x-2 items-center px-2 py-2 w-full"
              :class="[
                isIdInAccountIds(data.id).value
                  ? 'text-blue-500 bg-blue-100 border-2 border-blue-500'
                  : 'shadow border'
              ]"
            >
              <div class="flex-shrink-0">
                <!-- <input class="rounded" v-model="formData.accountIds" type="checkbox" :name="data.business_name" :value="data.id" /> -->
              </div>
              <div class="text-lg w-full break-words">
                {{ data.business_name ?? 'N/A' }}
              </div>
            </button>
          </div>
          <div class="flex justify-between mt-3">
            <BaseButton
              label="Back"
              color="contrast"
              @click="goToAddUserStep(2)"
              :disabled="loading"
              :loading="loading"
              outline
            />
            <BaseButton
              label="Finish"
              color="info"
              @click="addUserFn()"
              :disabled="loading"
              :loading="loading"
              outline
            />
          </div>
        </div>
        <div v-if="addUserStep == 4">
          <div class="grid grid-cols-1">
            <BaseIcon :path="mdiLoading" size="80" w="" h="h-40" class="p-1 h-full animate-spin" />
            <div class="text-2xl text-center">Please Wait...</div>
          </div>
        </div>
        <div v-if="addUserStep == 5">
          <div class="grid grid-cols-1 border-t my-2">
            <BaseIcon
              :path="mdiCheckboxMarkedCircleOutline"
              size="80"
              w=""
              h="h-40"
              class="p-1 h-full text-green-500"
            />
            <div class="text-2xl text-center">A new user has been successfully added.</div>
          </div>
          <div class="flex justify-between mt-3">
            <BaseButton
              label="Close"
              color="contrast"
              @click="clearstates()"
              :disabled="loading"
              :loading="loading"
              outline
            />
          </div>
        </div>
      </CardBoxModal>

      <CardBox rounded="rounded-sm" class="mb-6 px-2" has-table style="overflow: auto">
        <div class="flex py-2 justify-between items-center" style="overflow: auto">
          <div class="flex justify-items-end items-center space-x-5">
            <FormControl v-model="searchValue" type="search" name="search" placeholder="Search" />
            <BaseIcon
              @click="reloadData()"
              :path="mdiReload"
              size="30"
              w=""
              h="h-12"
              class="text-gray-600 p-1 h-full border border-black rounded cursor-pointer hover:bg-black hover:text-white"
              title="Reload"
            />
          </div>
          <div>
            <BaseButton @click="startAddUser(true)" :icon="mdiPlus" label="User" color="info" />
          </div>
        </div>
        <DatabaseLoaderOne v-if="items.length == 0 && mainLoader" />
        <NoRecordSvgVue v-else-if="items.length == 0 && mainLoader == false" />
        <table v-else>
          <thead class="text-sm">
            <tr>
              <th>FULLNAME</th>
              <th>EMAIL</th>
              <th>PHONE</th>
              <th>COMPANY</th>
              <th>CREATED</th>
              <th>ACTION</th>
            </tr>
          </thead>
          <tbody class="text-sm">
            <tr v-for="data in items" :key="data.id">
              <td data-label="FULLNAME">
                {{ data.name ?? '-' }}
              </td>
              <td data-label="EMAIL">
                {{ data.email ?? '-' }}
              </td>
              <td data-label="PHONE">
                {{ data.phone_number ?? '-' }}
              </td>
              <td data-label="COMPANY">
                {{ data.company?.name ?? '-' }}
              </td>
              <td data-label="CREATED">
                {{ new Date(data.created_at).toLocaleString() }}
              </td>
              <td class="before:hidden lg:w-1 whitespace-nowrap">
                <BaseButtons type="justify-start lg:justify-end" no-wrap>
                  <BaseButton
                    @click="routeTo(data)"
                    :icon="mdiEye"
                    :label="
                      data.role == 'SUPERADMIN' || data.role == authUserData.role
                        ? 'View'
                        : 'Manage'
                    "
                    color="contrast"
                    outline
                    small
                  />
                </BaseButtons>
              </td>
            </tr>
          </tbody>
        </table>

        <div class="p-3 lg:px-6 border-t border-gray-100 dark:border-slate-800" v-if="!mainLoader">
          <vue-awesome-paginate
            :total-items="companyStore.total_users"
            :items-per-page="companyStore.limit"
            :max-pages-shown="5"
            v-model="currentPage"
            @click="onClickHandler"
          />
        </div>
      </CardBox>
    </SectionMainFull>
  </LayoutAuthenticated>
</template>
<script setup>
import BaseButton from '@/components/BaseButton.vue'
import BaseButtons from '@/components/BaseButtons.vue'
import BaseIcon from '@/components/BaseIcon.vue'
import BaseLevel from '@/components/BaseLevel.vue'
import CardBox from '@/components/CardBox.vue'
import CardBoxModal from '@/components/CardBoxModal.vue'
import FormControl from '@/components/FormControl.vue'
import FormField from '@/components/FormField.vue'
import DatabaseLoaderOne from '@/components/Reusables/Loaders/DatabaseLoaderOne.vue'
import NoRecordSvgVue from '@/components/Reusables/Svgs/NoRecordSvg.vue'
import FormErrorMessage from '@/components/FormErrorMessage.vue'
import SectionMainFull from '@/components/SectionMainFull.vue'
import SectionTitleLineWithButton from '@/components/SectionTitleLineWithButton.vue'
import { useInfo, useSuccess, useWarning } from '@/helpers/functions/notifications.js'
import { useRoute, useRouter } from 'vue-router'
import LayoutAuthenticated from '@/layouts/LayoutAuthenticated.vue'
import { useAuthStore } from '@/stores/auth.js'
import { useClientStore } from '@/stores/client.js'
import { useCompanyStore } from '@/stores/company.js'
import { useUserStore } from '@/stores/user.js'
import {
  mdiCashClock,
  mdiAccount,
  mdiAccountTie,
  mdiReload,
  mdiEye,
  mdiPlus,
  mdiLoading,
  mdiCheckboxMarkedCircleOutline
} from '@mdi/js'
import { computed, defineProps, onMounted, ref, watch, watchEffect } from 'vue'
const router = useRouter()
const route = useRoute()
const infoPush = useInfo()
const successPush = useSuccess()
const warningPush = useWarning()
const companyStore = useCompanyStore()
const authStore = useAuthStore()
const clientStore = useClientStore()
const userStore = useUserStore()
const isAddUserModalActive = ref(false)
const perPage = ref(5)
const currentPage = ref(1)
const checkedRows = ref([])
const checkedDeleteRows = ref([])
const selectedViewData = ref([])
const searchValue = ref('')
const loaderMessage = ref('')
const mainLoader = ref(false)
const loading = ref(false)
const addUserStep = ref(1)

onMounted(async () => {
  try {
    mainLoader.value = true
    await companyStore.getCompanyUsers(authStore.companyData.id, currentPage.value)
  } catch (e) {
  } finally {
    mainLoader.value = false
  }
})
const { checkable } = defineProps({
  checkable: Boolean
})

const items = computed(() => {
  const query = searchValue.value.toLowerCase()
  return Array.isArray(companyStore.users)
    ? companyStore.users.filter(
        (data) =>
          data.name.toLowerCase().includes(query) ||
          data.email.toLowerCase().includes(query) ||
          data.phone_number.includes(query)
      )
    : []
})
const formData = ref({
  accountIds: [],
  name: '',
  email: '',
  phone: '',
  companyId: '',
  role: ''
})
const validateForm = ref({
  accountIds: '',
  name: '',
  email: '',
  phone: '',
  companyId: '',
  role: ''
})
const selectOptions = [
  { id: 'ADMIN', label: 'ADIMIN' },
  { id: 2, label: 'Marketing' },
  { id: 3, label: 'Sales' }
]

const companyData = computed(() => {
  return authStore.companyData
})
const authUserData = computed(() => {
  return authStore.userData
})

const clientsData = computed(() => {
  return authStore.clientsData
})
const goToStepTwo = computed(
  () =>
    validateForm.value.name == true &&
    validateForm.value.phone == true &&
    validateForm.value.email == true
)

const itemsPaginated = computed(() =>
  items.value.slice(perPage.value * currentPage.value, perPage.value * (currentPage.value + 1))
)
const numPages = computed(() => Math.ceil(items.value.length / perPage.value))
const currentPageHuman = computed(() => currentPage.value + 1)
const maxPaginationButtons = 2
const visiblePages = computed(() => {
  const pagesList = []
  const start = Math.max(0, currentPage.value - maxPaginationButtons)
  const end = Math.min(numPages.value - 1, currentPage.value + maxPaginationButtons)
  return items.value.slice(start, end + 1)
})

watch(currentPage, async () => {
  // router.push({ ...route, query: { page: currentPage.value } })
  try {
    mainLoader.value = true
    await companyStore.getCompanyUsers(authStore.companyData.id, currentPage.value)
  } catch (e) {
  } finally {
    mainLoader.value = false
  }
})

const startAddUser = (state) => {
  formData.value.companyId = companyData.value.id
  isAddUserModalActive.value = state
}
const setRoleType = (type) => {
  formData.value.role = type
}
const addAccountId = (id) => {
  const index = formData.value.accountIds.indexOf(id)
  if (index === -1) {
    formData.value.accountIds.push(id)
  } else {
    formData.value.accountIds.splice(index, 1)
  }
}

const isIdInAccountIds = (id) => {
  return computed(() => formData.value.accountIds.includes(id))
}

const goToAddUserStep = (step) => {
  addUserStep.value = step
}
const reloadData = async () => {
  try {
    mainLoader.value = true
    await companyStore.getCompanyUsers(authStore.companyData.id, currentPage.value)
  } catch (e) {
  } finally {
    mainLoader.value = false
  }
}

const routeTo = async (data) => {
  router.push({ name: 'user-profile', params: { id: data.id } })
}
const addUserFn = async () => {
  addUserStep.value = 4
  loading.value = true
  try {
    const data = JSON.stringify({
      name: formData.value.name,
      email: formData.value.email,
      phone_number: formData.value.phone,
      company_id: formData.value.companyId,
      accounts: formData.value.accountIds,
      role: formData.value.role
    })
    let res = await userStore.addUser(data)
    if (res == 201) {
      addUserStep.value = 5
      await companyStore.getCompanyUsers(authStore.companyData.id)
    } else if (res == 403) {
      warningPush('Not allowed', 'You are not allowed to perform this action')
      addUserStep.value = 3
    } else if (res == 404) {
      warningPush('Something is missing', 'Kindly check your input')
      addUserStep.value = 3
    } else {
      warningPush('Something went wrong', 'Please try again.')
      addUserStep.value = 3
    }
  } catch (error) {
    addUserStep.value = 3

    warningPush('Unexpected error occured')
  } finally {
    loading.value = false
  }
}
const inputValidator = (e) => {
  const inputName = e.target.name
  switch (inputName) {
    case 'fullname':
      const fullnameRegex = /.{4,}/
      validateForm.value.name = fullnameRegex.test(e.target.value)
      break
    case 'phone':
      const phoneRegex = /^\d{12}$/
      validateForm.value.phone = phoneRegex.test(e.target.value)
      break
    case 'email':
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      validateForm.value.email = emailRegex.test(e.target.value)
      break
    default:
      break
  }
}
const clearstates = () => {
  addUserStep.value = 1
  loading.value = false
  formData.value = []
  validateForm.value = []
  searchValue.value = ''
  currentPage.value = 0
  checkedRows.value = []
  checkedDeleteRows.value = []
  selectedViewData.value = []
  isAddUserModalActive.value = false
}
</script>

<style>
.pagination-container {
  display: flex;

  column-gap: 10px;
}

.paginate-buttons {
  height: 40px;

  width: 40px;

  border-radius: 20px;

  cursor: pointer;

  background-color: rgb(242, 242, 242);

  border: 1px solid rgb(217, 217, 217);

  color: black;
}

.paginate-buttons:hover {
  background-color: #d8d8d8;
}

.active-page {
  background-color: #3498db;

  border: 1px solid #3498db;

  color: white;
}

.active-page:hover {
  background-color: #2988c8;
}
</style>
