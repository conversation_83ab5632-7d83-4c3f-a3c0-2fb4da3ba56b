<template>
  <LayoutAuthenticated>
    <SectionMainFull>
      <SectionTitleLineWithButton :icon="mdiCashClock" title="Update Password" small>
      </SectionTitleLineWithButton>
      <div class="mb-8 bg-white p-4 shadow">
        <div class="grid grid-cols-1 md:gap-1 mb-3 max-w-sm">
          <FormField label="Current password*">
            <div>
              <FormControl
                v-model="passwordForm.password_current"
                type="password"
                name="password_current"
                placeholder="Enter your current password"
              />
            </div>
          </FormField>

          <FormField label="New password*">
            <div>
              <FormControl
                v-model="passwordForm.password"
                type="password"
                name="password"
                placeholder="Enter your new password"
              />
            </div>
          </FormField>

          <FormField label="Confirm password*">
            <div>
              <FormControl
                v-model="passwordForm.password_confirmation"
                type="password"
                name="password_confirmation"
                placeholder="Confirm new password"
              />
            </div>
          </FormField>
        </div>

        <BaseButton label="Update" color="info" @click="updatePassword" />
      </div>
      <Loading v-model="isLoading" />
    </SectionMainFull>
  </LayoutAuthenticated>
</template>
<script setup>
import BaseButton from '@/components/BaseButton.vue'
import FormControl from '@/components/FormControl.vue'
import FormField from '@/components/FormField.vue'
import Loading from '@/components/modals/Loading.vue'
import SectionMainFull from '@/components/SectionMainFull.vue'
import SectionTitleLineWithButton from '@/components/SectionTitleLineWithButton.vue'
import LayoutAuthenticated from '@/layouts/LayoutAuthenticated.vue'
import { reactive, ref } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { usePush } from 'notivue'

const isLoading = ref(false)

const authStore = useAuthStore()
const push = usePush()

const passwordForm = reactive({
  password_current: '',
  password: '',
  password_confirmation: ''
})

const updatePassword = async () => {
  if (
    !passwordForm.password ||
    !passwordForm.password_confirmation ||
    !passwordForm.password_current
  ) {
    push.error({ message: 'Kindly fill out all the fields', duration: 1000 })
    return
  }

  if (passwordForm.password != passwordForm.password_confirmation) {
    push.error({ message: 'New password do not match', duration: 1000 })
    return
  }

  isLoading.value = true

  const status = await authStore.updatePassword(
    passwordForm.password_current,
    passwordForm.password
  )

  isLoading.value = false

  if (status != 200) {
    if (status == 400) {
      push.error({ message: 'Wrong current password', duration: 1000 })
    } else {
      push.error({ message: 'Something went wrong', duration: 1000 })
    }
  } else {
    push.success({ message: 'Password updated', duration: 1000 })
  }
}
</script>

<style src="vue-multiselect/dist/vue-multiselect.css"></style>

<style>
.multiselect__option--highlight {
  background: #eee !important;
  outline: none;
  color: black;
}

.multiselect__option--highlight::after {
  content: attr(data-select);
  background: #eee !important;
  color: black;
}

.multiselect__input:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.125);
}

.pagination-container {
  display: flex;

  column-gap: 10px;
}

.paginate-buttons {
  height: 40px;

  width: 40px;

  border-radius: 10px;

  cursor: pointer;

  background-color: rgb(242, 242, 242);

  border: 1px solid rgb(217, 217, 217);

  color: black;
}

.paginate-buttons:hover {
  background-color: #d8d8d8;
}

.active-page {
  background-color: #3498db;

  border: 1px solid #3498db;

  color: white;
}

.active-page:hover {
  background-color: #2988c8;
}
</style>
