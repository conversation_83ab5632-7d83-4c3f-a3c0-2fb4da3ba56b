<template>
    <div class="grid grid-cols-1 lg:grid-cols-1">
      <div class="text-lg">
        You are about to refund ZMW {{ props.transaction?.amount ?? "N/A" }} (Transaction ID: {{ props?.transaction?.external_id ?? "N/A" }}).
      </div>
      <div class="text-lg mt-3 font-semi-bold text-red-500">
        This action is irreversible once confirmed.
      </div>
    </div>
    <div class="flex justify-between mt-3">
      <BaseButton label="Cancel" color="" outline @click="closeModal" />
      <BaseButton label="Refund" color="info" outline @click="refundTransaction" />
    </div>
  <Loading v-model="isLoading" />
</template>

<script setup>
import { reactive, ref } from 'vue'
import BaseButton from '@/components/BaseButton.vue'
import { useTransactionStore } from '@/stores/transactions'
import { usePush } from 'notivue'
import Loading from '../Loading.vue'

const props = defineProps(['transaction'])
const isLoading = ref(false)
const push = usePush()
const transactionStore = useTransactionStore()
const emit = defineEmits(['refund-modal-emit'])

const closeModal = (event, item) => {
  emit('refund-modal-emit', event, item)
}

async function refundTransaction() {
  if (props.transaction.external_id == "" || props.transaction.external_id == null || props.transaction.external_id.length <= 0) {
    push.error({ message: 'Transaction does not have external reference', duration: 1000 })
    return
  }

  isLoading.value = true
  const status = await transactionStore.refundTransaction({
    external_id: props.transaction.external_id
  })
  isLoading.value = false

  if (status != 200) {
    if (status == 404) {
      push.error({ message: 'Transaction not found', duration: 1000 })
    } else {
      push.error({ message: 'Something went wrong', duration: 1000 })
    }
  } else {
    push.success({ message: 'Refund processing', duration: 1000 })
  }
}
</script>

<style lang="scss" scoped></style>
