<template>
  <LayoutAuthenticated>
    <SectionMainFull>
      <SectionTitleLineWithButton :icon="mdiDomain" title="Accounts" small>
      </SectionTitleLineWithButton>

      <div>
        <div
          class="p-8 mx-auto bg-white shadow-[0_2px_10px_-3px_rgba(6,81,237,0.3)] text-[#333] font-[sans-serif]"
        >
          <div class="flex justify-items-end items-center space-x-5 mb-3">
            <FormControl
              v-model="searchValue"
              type="search"
              name="search"
              placeholder="Search"
              class="border-red-500"
            />
          </div>
          <div class="space-y-4 items-start">
            <div
              to="/login"
              @click="routeToAccount(data)"
              v-for="(data, index) in paginatedAccounts"
              :key="index"
              class="flex space-x-2 items-center border h-14 w-full bg-gray-50 rounded align-middle hover:bg-gray-100 cursor-pointer"
            >
              <BaseIcon :path="mdiDomain" size="30" w="" h="h-10" class="text-gray-600 px-2" />
              <div>
                <div class="">{{ data.business_name ?? 'N/A' }}</div>
                <div class="text-xs flex items-center space-x-0">
                  <BaseIcon
                    :path="mdiMapMarker"
                    size="12"
                    w=""
                    h=""
                    class="text-gray-500"
                  /><span>{{ data.address ?? 'N/A' }}</span>
                </div>
              </div>
              <div class="ml-auto" style="margin-left: auto">
                <BaseIcon
                  :path="mdiArrowRightCircleOutline"
                  size="30"
                  w=""
                  h="h-10"
                  class="text-gray-600 px-2"
                />
              </div>
            </div>
          </div>
          <div class="dark:border-slate-800 mt-3" v-if="numPages > 1">
            <BaseLevel class="flex justify-between">
              <BaseButtons class="flex gap-2">
                <!-- Start button -->
                <BaseButton
                  :label="'Start'"
                  :color="currentPage === 0 ? 'lightDark' : 'whiteDark'"
                  :disabled="currentPage === 0"
                  small
                  @click="goToPage(0)"
                />

                <!-- Previous button -->
                <BaseButton
                  :label="'Previous'"
                  :color="currentPage === 0 ? 'lightDark' : 'whiteDark'"
                  :disabled="currentPage === 0"
                  small
                  @click="previousPage"
                />

                <!-- Next button -->
                <BaseButton
                  :label="'Next'"
                  :color="currentPage === numPages - 1 ? 'lightDark' : 'whiteDark'"
                  small
                  :disabled="currentPage === numPages - 1"
                  @click="nextPage"
                />

                <!-- End button -->
                <BaseButton
                  :label="'End'"
                  :color="currentPage === numPages - 1 ? 'lightDark' : 'whiteDark'"
                  :disabled="currentPage === numPages - 1"
                  small
                  @click="goToPage(numPages - 1)"
                />
              </BaseButtons>
              <small>Page {{ currentPageHuman }} of {{ numPages }}</small>
            </BaseLevel>
          </div>
        </div>
      </div>
    </SectionMainFull>
  </LayoutAuthenticated>
</template>

<script setup>
import { computed, ref, onMounted, watch } from 'vue'
import { useAuthStore } from '@/stores/auth.js'
import { useRouter, useRoute } from 'vue-router'
import LayoutAuthenticated from '@/layouts/LayoutAuthenticated.vue'
import SectionTitleLineWithButton from '@/components/SectionTitleLineWithButton.vue'
import SectionMainFull from '@/components/SectionMainFull.vue'
import { mdiDomain, mdiArrowRightCircleOutline, mdiMapMarker, mdiReload, mdiPhone } from '@mdi/js'
import FormControl from '@/components/FormControl.vue'
import FormField from '@/components/FormField.vue'
import * as chartConfig from '@/components/Charts/chart.config.js'
import BaseIcon from '@/components/BaseIcon.vue'
import { computedAsync } from '@vueuse/core'
import { logout } from '@/helpers/functions/auth.js'
import BaseButton from '@/components/BaseButton.vue'
const auth = useAuthStore()

const searchedAccounts = ref([])
const searchValue = ref('')
const perPage = ref(15)
const currentPage = ref(0)

const companyData = computed(() => {
  return auth.companyData
})

const currentPageHuman = computed(() => currentPage.value + 1)

const clientsData = computed(() => {
  return auth.clientsData
})

const accounts = computed(() => {
  return searchValue.value?.length
    ? searchedAccounts.value
    : Array.isArray(clientsData.value)
      ? clientsData.value
      : []
})

const paginatedAccounts = computed(() => {
  return accounts.value.slice(
    perPage.value * currentPage.value,
    perPage.value * (currentPage.value + 1)
  )
})

const numPages = computed(() => Math.ceil(accounts.value.length / perPage.value))

const goToPage = (page) => {
  currentPage.value = page
}

const previousPage = () => {
  if (currentPage.value > 0) {
    currentPage.value -= 1
  }
}

const nextPage = () => {
  if (currentPage.value < numPages.value - 1) {
    currentPage.value += 1
  }
}

const chartData = ref(null)
const viewButton = ref(false)
const fillChartData = () => {
  chartData.value = chartConfig.sampleChartData()
}
const showViewButton = (state) => {
  viewButton.value = state
}
onMounted(async () => {
  if (companyData == '' || companyData == null) {
    logout()
  }
  fillChartData()
  localStorage.removeItem('account')
})

const routeToAccount = async (data) => {
  localStorage.setItem('account', JSON.stringify(data))
  window.location.href = '/account/dashboard'
}

watch(searchValue, () => {
  currentPage.value = 0
  searchedAccounts.value = clientsData.value.filter((account) =>
    account.business_name.toLowerCase().includes(searchValue.value.toLowerCase())
  )
})
</script>

<style scoped>
.accountbg {
  background: url('@/assets/images/backgrounds/bg-1.jpg');
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  background-size: cover;
}
</style>
