import {
  mdiCash,
  mdiLink,
  mdiViewList,
  mdiDomain,
  mdiAccountGroupOutline,
  mdiChartPieOutline,
  mdiCashMultiple,
  mdiArrowULeftTop,
  mdiSync,
  mdiCreditCardOutline,
  mdiCellphone,
  mdiCogOutline
} from '@mdi/js'
import Permissions from './helpers/permissions'

export default [
  {
    to: '/company-hub/home',
    label: 'Dashboard',
    icon: mdiCash,
    companyHubView: true
  },
  {
    to: '/company-hub/accounts',
    label: 'Accounts',
    icon: mdiDomain,
    companyHubView: true
  },
  {
    to: '/company-hub/statistics',
    label: 'Statistics',
    icon: mdiChartPieOutline,
    companyHubView: true
  },
  {
    to: '/company-hub/users',
    label: 'Users',
    icon: mdiAccountGroupOutline,
    companyHubView: true
  },
  {
    label: 'Recons',
    icon: mdiSync,
    companyHubView: true,
    menu: [
      {
        to: '/company-hub/recons/card',
        label: 'Card Recons',
        icon: mdiCreditCardOutline,
        companyHubView: true
      },
      {
        to: '/company-hub/recons/mobile',
        label: 'Mobile Recons',
        icon: mdiCellphone,
        companyHubView: true
      },
      {
        to: '/company-hub/recons/configs',
        label: 'Recons Configs',
        icon: mdiCogOutline,
        companyHubView: true,
        permission: Permissions.SUPERADMIN_ONLY
      }
    ]
  },
  {
    label: 'Reports',
    icon: mdiViewList,
    companyHubView: true,
    menu: [
      {
        to: '/company-hub/reports/accounts/summary',
        label: 'Accounts Summary',
        companyHubView: true
      }
    ]
  },
  {
    to: '/company-hub/accounts',
    icon: mdiArrowULeftTop,
    label: 'Go to Hub',
    accountHubView: true
  },
  {
    to: '/account/dashboard',
    icon: mdiChartPieOutline,
    label: 'Dashboard',
    accountHubView: true
  },
  {
    to: '/account/transactions',
    label: 'Transactions',
    icon: mdiCashMultiple,
    accountHubView: true
  },
  {
    to: '/account/payment-links',
    label: 'Payment links',
    icon: mdiLink,
    accountHubView: true,
    permission: Permissions.CHECKOUT_SERVICES
  }
  // {
  //   to: '/forms',
  //   label: 'Forms',
  //   icon: mdiSquareEditOutline
  // },
  // {
  //   to: '/ui',
  //   label: 'UI',
  //   icon: mdiTelevisionGuide
  // },
  // {
  //   to: '/responsive',
  //   label: 'Responsive',
  //   icon: mdiResponsive
  // },
  // {
  //   to: '/',
  //   label: 'Styles',
  //   icon: mdiPalette
  // },
  // {
  //   to: '/profile',
  //   label: 'Profile',
  //   icon: mdiAccountCircle
  // },
  // {
  //   to: '/login',
  //   label: 'Login',
  //   icon: mdiLock
  // },
  // {
  //   to: '/error',
  //   label: 'Error',
  //   icon: mdiAlertCircle
  // },
  // {
  //   label: 'Dropdown',
  //   icon: mdiViewList,
  //   menu: [
  //     {
  //       label: 'Item One'
  //     },
  //     {
  //       label: 'Item Two'
  //     }
  //   ]
  // },
  // {
  //   href: 'https://github.com/justboil/admin-one-vue-tailwind',
  //   label: 'GitHub',
  //   icon: mdiGithub,
  //   target: '_blank'
  // },
  // {
  //   href: 'https://github.com/justboil/admin-one-react-tailwind',
  //   label: 'React version',
  //   icon: mdiReact,
  //   target: '_blank'
  // }
]
