import { defineStore } from 'pinia'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'vue-router'
import { BASE_URL, api, authURL } from '@/helpers/api/http-commons'
import axios from 'axios'

export const useAuthStore = defineStore('auth', {
  state: () => {
    return {
      authenticated: false,
      user: []
    }
  },
  getters: {
    userData() {
      const storedUser = localStorage.getItem('profile')
      const parsedUser = JSON.parse(storedUser)
      return parsedUser
    },
    companyData() {
      const storedCompany = localStorage.getItem('company')
      const parsedCompany = JSON.parse(storedCompany)
      return parsedCompany
    },
    clientsData() {
      const storedClients = localStorage.getItem('clients')
      const parsedClients = JSON.parse(storedClients)
      return parsedClients
    },
    accountData() {
      const storedClients = localStorage.getItem('account')
      const parsedClients = JSON.parse(storedClients)
      return parsedClients
    }
  },
  actions: {
    async onLogin(email, password) {
      try {
        const response = await axios.post(
          BASE_URL + authURL.LOGIN,
          {
            email: email,
            password: password
          },
          {
            headers: {
              'Content-Type': 'application/json'
            }
          }
        )
        console.log(response.data)
        return response
      } catch (error) {
        console.error('Error:', error)
        return error.response
      }
    },
    async onSubmitOtp(otp) {
      console.log(otp)
      try {
        const response = await axios.post(
          BASE_URL + authURL.SUBMIT_OTP,
          {
            token: otp
          },
          {
            headers: {
              'Content-Type': 'application/json'
            }
          }
        )
        console.log(response.data)
        localStorage.setItem('profile', JSON.stringify(response.data.user))
        localStorage.setItem('company', JSON.stringify(response.data.company))
        localStorage.setItem('clients', JSON.stringify(response.data.clients))
        return response.status
      } catch (error) {
        console.error('Error:', error)
        return error.response.status
      }
    },
    async forgotPassword(email) {
      try {
        const response = await axios.post(
          BASE_URL + authURL.FORGOT_PASSWORD,
          {
            email: email
          },
          {
            headers: {
              'Content-Type': 'application/json'
            }
          }
        )
        console.log(response)
        return response.status
      } catch (error) {
        console.error('Error:', error)
        return error.response.status
      }
    },
    async resetPassword(password, token) {
      try {
        const response = await axios.post(
          BASE_URL + authURL.RESET_PASSWORD + token,
          {
            new_password: password
          },
          {
            headers: {
              'Content-Type': 'application/json'
            }
          }
        )
        console.log(response)
        return response.status
      } catch (error) {
        console.error('Error:', error)
        return error.response.status
      }
    },
    async updateProfile(data) {
      try {
        const response = await axios.patch(BASE_URL + api.USERS.UPDATE_PROFILE, data, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        console.log(response)
        return response
      } catch (error) {
        console.error('Error:', error)
        return error.response
      }
    },
    async completeEmailChange(token) {
      try {
        const response = await axios.post(
          BASE_URL + api.USERS.COMPLETE_EMAIL_CHANGE + token,
          {},
          {
            headers: {
              'Content-Type': 'application/json'
            }
          }
        )
        console.log(response)
        return response
      } catch (error) {
        console.error('Error:', error)
        return error.response
      }
    },
    async updatePassword(current_password, new_password) {
      try {
        const response = await axios.patch(
          BASE_URL + api.USERS.UPDATE_PASSWORD,
          {
            new_password,
            current_password
          },
          {
            headers: {
              'Content-Type': 'application/json'
            }
          }
        )
        console.log(response)
        return response.status
      } catch (error) {
        console.error('Error:', error)
        return error.response.status
      }
    }
  }
})
