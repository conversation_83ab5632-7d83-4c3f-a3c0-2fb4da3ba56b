<script setup>
import { ref, watch, computed, onMounted } from "vue";
import {
  Chart,
  LineElement,
  PointElement,
  LineController,
  LinearScale,
  CategoryScale,
  Tooltip,
  BarElement,
  BarController,
} from "chart.js";

const props = defineProps({
  data: {
    type: Object,
    required: true,
  },
});

const root = ref(null);

let chart;

Chart.register(
  BarElement,
  LineElement,
  PointElement,
  BarController,
  LineController,
  LinearScale,
  CategoryScale,
  Tooltip
);

onMounted(() => {
  chart = new Chart(root.value, {
    type: "bar",
    data: props.data,
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          display: true,
        },
        x: {
          display: true,
        },
      },
      plugins: {
        legend: {
          position: 'top',
          display: true,
        },
        title: {
        display: true,
        text: 'Chart.js Bar Chart'
      }
      },
    },
  });
});

const chartData = computed(() => props.data);

watch(chartData, (data) => {
  if (chart) {
    chart.data = data;
    chart.update();
  }
});
</script>

<template>
  <canvas ref="root" />
</template>
