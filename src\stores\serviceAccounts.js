import { defineStore } from 'pinia'
import { BASE_URL, api } from '@/helpers/api/http-commons.js'
import axios from 'axios'

export const useServiceAccountsStore = defineStore('serviceAccounts', {
  state: () => {
    return {
      floatData: []
    }
  },
  getters: {},
  actions: {
    async getFloat(clientId) {
      try {
        let config = {
          method: 'get',
          maxBodyLength: Infinity,
          url: BASE_URL + api.SERVICE_ACCOUNTS.BASE + api.SERVICE_ACCOUNTS.GET_FLOAT + `/${clientId}`,
          headers: {
            'Content-Type': 'application/json'
          }
        }
        const response = await axios.request(config)
        
        this.floatData = response.data;
        return response.status
      } catch (error) {
        return error.response.status
      }
    }
  }
})
