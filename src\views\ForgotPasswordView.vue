<template>
  <!-- This is an example component -->
  <div class="h-screen font-sans login bg-cover">
    <div class="container mx-auto h-full flex flex-1 justify-center items-center">
      <div class="w-full max-w-lg">
        <div class="leading-loose">
          <form
            class="max-w-sm m-4 p-10 bg-white bg-opacity-25 rounded shadow-xl"
            style="background-color: #ffffffb0"
          >
            <div class="flex justify-center items-center">
              <img :src="Logo" style="height: 60px" />
            </div>
            <h1 class="text-2xl font-semibold mb-4"></h1>
            <p class="text-xl font-semibold mb-4 text-center">ENTER YOUR EMAIL ADDRESS</p>
            <div class="mb-4">
              <FormField label="">
                <FormControl
                  v-model="form.email"
                  placeholder="Email"
                  type="email"
                  :icon="mdiEmailOutline"
                />
              </FormField>
            </div>
            <div class="mt-4 items-center flex justify-between">
              <BaseButton
                :disabled="!emailMatch"
                color="info"
                label="RESET"
                class="w-full"
                @click="submit()"
              />
              <!-- < -->
            </div>
            <div class="text-center">
              <div>
                <router-link
                  to="/login"
                  class="inline-block right-0 align-baseline text-sm text-500 text-black hover:text-gray-500"
                  href="#"
                  >Already have an account</router-link
                >
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { reactive, ref, computed, onBeforeMount } from 'vue'
import { useRouter,useRoute } from 'vue-router'
import { mdiLockOutline, mdiEmailOutline, mdiShieldLockOutline } from '@mdi/js'
import SectionFullScreen from '@/components/SectionFullScreen.vue'
import CardBox from '@/components/CardBox.vue'
import FormCheckRadio from '@/components/FormCheckRadio.vue'
import FormField from '@/components/FormField.vue'
import FormControl from '@/components/FormControl.vue'
import BaseButton from '@/components/BaseButton.vue'
import BaseButtons from '@/components/BaseButtons.vue'
import LayoutGuest from '@/layouts/LayoutGuest.vue'
import BaseIcon from '@/components/BaseIcon.vue'
import Logo from '@/assets/images/logos/logo.png'
import Background from '@/assets/images/backgrounds/bg-1.jpg'
import { useAuthStore } from '@/stores/auth.js'
import { useInfo, useSuccess, useWarning } from '@/helpers/functions/notifications.js'
const infoPush = useInfo()
const successPush = useSuccess()
const warningPush = useWarning()
const auth = useAuthStore()
const loading = ref(false)
const otpStep = ref(false)
const form = reactive({
  email: '',
  password: '',
  confirmPassword: '',
  remember: true,
  otp: ''
})

const router = useRouter()
const route = useRoute()

// const submit = () => {

//   router.push('/company')
// }
const emailMatch = computed(() => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(form.email)
})

const submit = async () => {
  loading.value = true
  try {
    let rs = await auth.forgotPassword(form.email)

    if (rs == 200) {
      successPush('Email has been sent.', 'Check your email with password reset instructions.')
      clearstates()
      successPush('Email has been sent.', 'Check your email with password reset instructions.')
      clearstates()
    } else if (rs == 400) {
      infoPush('Failed to Proceed', 'Check your input.')
    } else if (rs == 404) {
      infoPush('Email does not exist', 'Enter correct email address.')
      infoPush('Failed to Proceed', 'Check your input.')
    } else if (rs == 404) {
      infoPush('Email does not exist', 'Enter correct email address.')
    } else {
      warningPush('Something went wrong', 'Please try again.')
      warningPush('Something went wrong', 'Please try again.')
    }
  } catch (error) {
    warningPush('Something went wrong', 'Please try again.')
  } finally {
    loading.value = false
  }
}

const register = () => {
  router.push('/register')
}
const goBack = () => {
  clearstates()
}
const clearstates = () => {
  form.email = ''
  form.password = ''
}
onBeforeMount(async () => {
  console.log(route.query)
  if (route.query && route.query.email) {
    form.email = route.query.email
    console.log('Email from URL:' + form.email)
  } else {
    console.log('No email in URL query.')
  }
})
</script>
<style scoped>
.login {
  background: url('@/assets/images/backgrounds/bg-1.jpg');
  background-repeat: no-repeat;
  background-size: cover;
}
</style>
