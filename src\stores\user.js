import { defineStore } from 'pinia'
import { BASE_URL, api } from '@/helpers/api/http-commons.js'
import axios from 'axios'

export const useUserStore = defineStore('user', {
  state: () => {
    return {
      users: [],
      user: []
    }
  },
  getters: {},
  actions: {
    async addUser(data) {
      try {
        let config = {
          method: 'post',
          maxBodyLength: Infinity,
          url: BASE_URL + api.USERS.ADD_USER,
          headers: {
            'Content-Type': 'application/json'
          },
          data: data
        }
        const response = await axios.request(config)
        this.user = response.data
        return response.status
      } catch (error) {
        return error.response.status
      }
    },
    async getUserDetails(id) {
      try {
        let config = {
          method: 'get',
          maxBodyLength: Infinity,
          url: BASE_URL + api.USERS.GET_USER_BY_ID + id,
          headers: {
            'Content-Type': 'application/json'
          }
        }
        const response = await axios.request(config)
        this.user = response.data
        return response.status
      } catch (error) {
        return error.response.status
      }
    },
    async deleteUser(id) {
      try {
        let config = {
          method: 'delete',
          maxBodyLength: Infinity,
          url: BASE_URL + api.USERS.DELETE_USER + id,
          headers: {
            'Content-Type': 'application/json'
          }
        }
        const response = await axios.request(config)
        this.users = response.data.data
        return response.status
      } catch (error) {
        return error.response.status
      }
    },
    async updateUser(data, id) {
      try {
        let config = {
          method: 'patch',
          maxBodyLength: Infinity,
          url: BASE_URL + api.USERS.UPDATE_USER + id,
          headers: {
            'Content-Type': 'application/json'
          },
          data: data
        }
        const response = await axios.request(config)
        this.users = response.data.data
        return response.status
      } catch (error) {
        return error.response.status
      }
    },
    async getLoginAttempts(username) {
      try {
        let data = {
          username: username
        }
        let config = {
          method: 'post',
          maxBodyLength: Infinity,
          url: BASE_URL + api.USERS.GET_LOGIN_ATTEMPTS,
          headers: {
            'Content-Type': 'application/json'
          },
          data: data
        }
        const response = await axios.request(config)
        return response;
      } catch (error) {
        return error.response;
      }
    },
    async setLoginAttempts(username) {
      try {
        let data = {
          username: username
        }
        let config = {
          method: 'post',
          maxBodyLength: Infinity,
          url: BASE_URL + api.USERS.SET_LOGIN_ATTEMPTS,
          headers: {
            'Content-Type': 'application/json'
          },
          data: data
        }
        const response = await axios.request(config)
        return response;
      } catch (error) {
        return error.response;
      }
    },
    async clearLoginAttempts(username) {
      try {
        let data = {
          username: username
        }
        let config = {
          method: 'post',
          maxBodyLength: Infinity,
          url: BASE_URL + api.USERS.CLEAR_LOGIN_ATTEMPTS,
          headers: {
            'Content-Type': 'application/json'
          },
          data: data
        }
        const response = await axios.request(config)
        return response;
      } catch (error) {
        return error.response;
      }
    }
  }
})
