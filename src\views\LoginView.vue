<template>
  <LayoutGuest>
    <div
      class="flex justify-center items-center h-screen .full-background overflow-hidden"
      style="background-color: #cdcee2"
    >
      <div class="w-full h-screen hidden lg:block">
        <img :src="Background" alt="Placeholder Image" class="object-cover w-full h-full" />
      </div>
      <div class="lg:p-36 md:p-52 sm:20 p-8 w-full lg:w-1/2">
        <div class="flex justify-center items-center">
          <img :src="Logo" style="height: 60px" />
        </div>
        <h1 class="text-2xl font-semibold mb-4"></h1>
        <div v-if="otpStep == false">
          <div class="fadeIn animated">
            <h1 class="text-xl font-semibold mb-4 text-center">Login</h1>
            <div class="mb-4">
              <FormField label="">
                <FormControl
                  v-model="form.email"
                  type="email"
                  placeholder="Email"
                  :icon="mdiEmailOutline"
                />
              </FormField>
            </div>
          </div>
          <div class="mb-4">
            <FormField label="">
              <FormControl
                v-model="form.password"
                placeholder="Password"
                type="password"
                :icon="mdiLockOutline"
              />
            </FormField>
          </div>
          <div class="mb-5">
            <router-link :to="{ name: 'forgot-password' }"> Forgot Password </router-link>
          </div>
          <div v-if="loading" class="flex justify-between cursor-not-allowed items-center w-full">
            <BaseButton class="w-full" color="info" label="Loading..." />
          </div>
          <div v-else class="flex justify-between items-center w-full">
            <BaseButton class="w-full" color="info" label="LOGIN" @click="submit()" />
          </div>
        </div>
        <!--  -->
        <div v-else>
          <div class="fadeIn animated">
            <h1 class="text-xl font-semibold mb-4 text-center">Enter OTP</h1>
            <div class="mb-4 flex items-center gap-1">
              <div class="flex items-center bg-white py-1 rounded w-full">
                <span class="whitespace-nowrap text-sm pl-2">{{ referenceCode }} - </span>
                <input
                  type="number"
                  v-model="form.otp"
                  class="border-none outline-none focus:ring-0 flex-1"
                  placeholder="Enter otp"
                />
              </div>
            </div>
          </div>
          <BaseButton class="w-full" v-if="loading == true" color="info" label="Loading..." />
          <!--  -->
          <div v-else class="w-full">
            <BaseButton class="w-full mb-5" color="info" label="VALIDATE" @click="submitOtp()" />
            <BaseButton class="w-full" color="contrast" label="GENERATE OTP" @click="goBack()" />
          </div>
        </div>
        <div class="mt-6 text-black text-center">
          <!-- <button @click="register()" class="hover:underline">Sign up Here</button> -->
        </div>
      </div>
    </div>
  </LayoutGuest>
</template>
<script setup>
import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { mdiLockOutline, mdiEmailOutline, mdiShieldLockOutline } from '@mdi/js'
import SectionFullScreen from '@/components/SectionFullScreen.vue'
import CardBox from '@/components/CardBox.vue'
import FormCheckRadio from '@/components/FormCheckRadio.vue'
import FormField from '@/components/FormField.vue'
import FormControl from '@/components/FormControl.vue'
import BaseButton from '@/components/BaseButton.vue'
import BaseButtons from '@/components/BaseButtons.vue'
import LayoutGuest from '@/layouts/LayoutGuest.vue'
import BaseIcon from '@/components/BaseIcon.vue'
import Logo from '@/assets/images/logos/logo.png'
import Background from '@/assets/images/backgrounds/bg-1.jpg'
import { useAuthStore } from '@/stores/auth.js'
import { useUserStore } from '@/stores/user.js'
import { useInfo, useSuccess, useWarning } from '@/helpers/functions/notifications.js'
const infoPush = useInfo()
const successPush = useSuccess()
const warningPush = useWarning()
const auth = useAuthStore()
const user = useUserStore()
const loading = ref(false)
const otpStep = ref(false)
const referenceCode = ref(null)
const form = reactive({
  email: '',
  password: '',
  remember: true,
  otp: ''
})

const router = useRouter()

// const submit = () => {

//   router.push('/company')
// }
const submit = async () => {
  loading.value = true
  try {
    let loginRes = await user.getLoginAttempts(form.email)
    if (loginRes.status == 403) {
      warningPush(
        'Too many login attempts',
        `Try again later, Remaining time: ${loginRes.data.remainingTime}s`
      )
      loading.value = false
      return
    }
    let rs = await auth.onLogin(form.email, form.password)
    if (rs.status == 200) {
      successPush('Authenticated!', 'Kindly enter OTP sent to your email.')
      otpStep.value = true
      referenceCode.value = rs.data.referenceCode
      clearstates()
    } else if (rs.status == 400) {
      warningPush('Failed to Proceed', 'Check your credentials.')
      await user.setLoginAttempts(form.email)
    }
  } catch (error) {
    warningPush('Something went wrong', 'Please try again.')
  } finally {
    loading.value = false
  }
}

const submitOtp = async () => {
  loading.value = true
  try {
    let rs = await auth.onSubmitOtp(form.otp.toString())
    if (rs == 200) {
      // successPush('Successfully logged in', '');
      localStorage.setItem('auth', true)
      await user.clearLoginAttempts(form.email)
      router.push('/company-hub/home')
    } else if (rs == 400) {
      warningPush('Failed to Proceed', 'Check your OTP.')
    }
  } catch (error) {
    warningPush('Something went wrong', 'Please try again.')
  } finally {
    loading.value = false
  }
}
const register = () => {
  router.push('/register')
}
const goBack = () => {
  clearstates()
  router.push('/login')
}
const clearstates = () => {
  form.email = ''
  form.password = ''
  form.otp = ''
}
</script>
<style scoped>
@import '../css/animations.css';

.full-background {
  background: url('/src/assets/images/backgrounds/bg-1.jpg');
  height: 100%;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}
</style>
