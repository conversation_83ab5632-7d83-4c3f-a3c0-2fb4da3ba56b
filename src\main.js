import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { notivue } from 'notivue'
import App from './App.vue'
import router from './router'
import axios from 'axios'
import { useMainStore } from '@/stores/main.js'
import Vue3EasyDataTable from 'vue3-easy-data-table'
import TextClamp from 'vue3-text-clamp'
import 'vue3-easy-data-table/dist/style.css'
import './css/main.css'
import 'notivue/notifications.css'
import 'notivue/animations.css'
import JsonExcel from 'vue-json-excel3'

import VueDatePicker from '@vuepic/vue-datepicker'
import VueAwesomePaginate from 'vue-awesome-paginate'

import '@vuepic/vue-datepicker/dist/main.css'
import 'vue-awesome-paginate/dist/style.css'
import { logout } from './helpers/functions/auth'
// Init Pinia
const pinia = createPinia()

// Create Vue app
const app = createApp(App)
app
  .use(router)
  .use(pinia)
  .use(VueAwesomePaginate)
  .use(TextClamp)
  .use(notivue, {
    position: 'top-right',
    pauseOnHover: false,
    limit: 20
  })
  .component('VueDatePicker', VueDatePicker)
  .component('downloadExcel', JsonExcel)
  .mount('#app')

// Init main store
const mainStore = useMainStore(pinia)

// Fetch sample data
mainStore.fetchSampleClients()
mainStore.fetchSampleHistory()

axios.interceptors.request.use((config) => {
  config.withCredentials = true
  return config
})

axios.interceptors.response.use(
  (response) => response, // Return the response if everything is fine
  (error) => {
    if (error.response && error.response.status === 401) {
      logout()
    }

    return Promise.reject(error)
  }
)

// Dark mode
// Uncomment, if you'd like to restore persisted darkMode setting, or use `prefers-color-scheme: dark`. Make sure to uncomment localStorage block in src/stores/darkMode.js
// import { useDarkModeStore } from './stores/darkMode'

// const darkModeStore = useDarkModeStore(pinia)

// if (
//   (!localStorage['darkMode'] && window.matchMedia('(prefers-color-scheme: dark)').matches) ||
//   localStorage['darkMode'] === '1'
// ) {
//   darkModeStore.set(true)
// }

// Default title tag
const defaultDocumentTitle = 'Primenet Next'

// Set document title from route meta
router.afterEach((to) => {
  document.title = to.meta?.title
    ? `${to.meta.title} — ${defaultDocumentTitle}`
    : defaultDocumentTitle
})
