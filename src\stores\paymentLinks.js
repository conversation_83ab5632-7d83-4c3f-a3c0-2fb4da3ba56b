import { defineStore } from 'pinia'
import { BASE_URL, api } from '@/helpers/api/http-commons.js'
import axios from 'axios'

export const usePaymentLinksStore = defineStore('paymentLinks', {
  state: () => {
    return {
      links: [],
      link: null
    }
  },
  getters: {},
  actions: {
    async getPaymentLink(id) {
      try {
        let config = {
          method: 'get',
          maxBodyLength: Infinity,
          url: BASE_URL + api.PAYMENT_LINKS.BASE + '/' + id,
          headers: {
            'Content-Type': 'application/json'
          }
        }
        const response = await axios.request(config)
        this.link = response.data?.link
        return response.status
      } catch (error) {
        return error.response.status
      }
    },
    async getPaymentLinks(clientId) {
      try {
        let config = {
          method: 'get',
          maxBodyLength: Infinity,
          url: BASE_URL + api.PAYMENT_LINKS.BASE + api.PAYMENT_LINKS.GET_BY_CLIENT + clientId,
          headers: {
            'Content-Type': 'application/json'
          }
        }
        const response = await axios.request(config)
        this.links = response.data.data
        return response.status
      } catch (error) {
        return error.response.status
      }
    },
    async createPaymentLink(data) {
      try {
        let config = {
          method: 'post',
          data: data,
          maxBodyLength: Infinity,
          url: BASE_URL + api.PAYMENT_LINKS.BASE,
          headers: {
            'Content-Type': 'application/json'
          }
        }
        const response = await axios.request(config)
        return response.status
      } catch (error) {
        return error.response.status
      }
    },
    async sharePaymentLink(data) {
      try {
        let config = {
          method: 'post',
          data: data,
          maxBodyLength: Infinity,
          url: BASE_URL + api.PAYMENT_LINKS.BASE + api.PAYMENT_LINKS.SHARE,
          headers: {
            'Content-Type': 'application/json'
          }
        }
        const response = await axios.request(config)
        return response.status
      } catch (error) {
        return error.response.status
      }
    }
  }
})
