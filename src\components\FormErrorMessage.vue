<template>
  <div class="text-xs text-red-500">
    {{ props.message }}
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { mdiCheckboxMarked } from '@mdi/js'
import BaseIcon from '@/components/BaseIcon.vue'
const emit = defineEmits(['checkoutRes'])
const props = defineProps({
  message: {
    type: String,
    default: null
  },
  color: {
    type: String,
    default: null
  },
  textsize: {
    type: String,
    default: null
  }
})
const answer = ref()
const textColor = computed(() => {
  return props.color
})
const textSize = computed(() => {
  return props.textsize
})
const changeState = (state) => {
  answer.value = state
  emit('checkoutRes', state)
}
</script>

<style scoped>
.default {
  color: v-bind(textColor);
  font-size: v-bind(textSize);
}
</style>
