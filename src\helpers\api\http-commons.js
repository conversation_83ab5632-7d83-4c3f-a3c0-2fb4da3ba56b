export const BASE_URL = 'http://localhost:5000';
export const BASE_URL_SLASH = 'http://localhost:5000/';
// export const BASE_URL = 'http://test.primenetpay.com:5000';
// export const BASE_URL_SLASH = 'http://test.primenetpay.com:5000/';
// export const BASE_URL = 'https://disburse.primenetpay.com:5001';
// export const BASE_URL_SLASH = 'https://disburse.primenetpay.com:5001/';
// export const BASE_URL = 'https://next.backend.primenetpay.com'
// export const BASE_URL_SLASH = 'https://next.backend.primenetpay.com/'

export const apiCredentials = {
  HEADERS: {
    USERNAME: 'test',
    PASSWORD: 'test'
  }
}

export const authURL = {
  SANCTUM_URL: '/sanctum/csrf-cookie',
  LOGIN: '/auth/login',
  SUBMIT_OTP: '/auth/complete-2fa',
  FORGOT_PASSWORD: '/auth/forgot-password',
  RESET_PASSWORD: '/auth/reset-password/',
  REGISTER: '/api/registration/store-customer',
  INIT_USER_DATA: '/api/initUserData',
  CHECK_USER: '/api/registration/check-username/'
}

export const api = {
  COMPANY: {
    GET_COMPANY_USERS: '/users/company/'
  },
  CLIENTS: {
    ASSIGN_USER_ACCOUNT: '/client-users',
    REVOKE_USER_ACCOUNT: '/client-users'
  },
  USERS: {
    ADD_USER: '/users',
    DELETE_USER: '/users/',
    UPDATE_USER: '/users/',
    UPDATE_PASSWORD: '/users/update-password',
    UPDATE_PROFILE: '/users/profile',
    COMPLETE_EMAIL_CHANGE: '/users/complete-email-change/',
    GET_USER_BY_ID: '/users/user-details/',
    SET_LOGIN_ATTEMPTS: '/users/set-login-attempts',
    CLEAR_LOGIN_ATTEMPTS: '/users/clear-login-attempts',
    GET_LOGIN_ATTEMPTS: '/users/get-login-attempts'
  },
  REPORTS: {
    BASE_URL: '/reports',
    ACCOUNTS_SUMMARIES: '/summary/accounts'
  },
  RECONS: {
    BASE_URL: '/recons',
    FILTERED: '/filter',
    UPDATE_RECONS_STATUS: '/status',
    EXPORT: '/export',
    CONFIGS: '/configs'
  },
  PAYMENT_LINKS: {
    BASE: '/payment-links',
    SHARE: '/share',
    GET_BY_CLIENT: '/client/'
  },
  SERVICE_ACCOUNTS: {
    BASE: '/service-accounts',
    GET_FLOAT: '/float'
  },
  TRANSACTION: {
    BASE: '/transactions',
    RECONCILE_TRANSACTION: '/reconcile',
    REFUND_TRANSACTION: '/refund',
    GET_CLIENT_TRANSACTIONS: '/client/',
    GET_CLIENT_FILTERED_TRANSACTIONS: '/filter/client/',
    FILTERED_TRANSACTIONS: '/filter',
    EXPORT: '/export'
  },
  DASHBOARD: {
    BASE: '/stats',
    GET_DAILY_CLIENTS_STATS: '/daily/client/',
    GET_DAILY_COMPANY_STATS: '/daily/company/',
    GET_DAILY_PRIMENET_STATS: '/daily',
    GET_MONTHLY_CLIENTS_STATS: '/monthly/client/',
    GET_MONTHLY_COMPANY_STATS: '/monthly/company/',
    GET_MONTHLY_PRIMENET_STATS: '/monthly'
  }
}
