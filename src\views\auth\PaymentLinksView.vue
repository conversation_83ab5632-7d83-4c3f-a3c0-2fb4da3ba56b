<template>
  <LayoutAuthenticated>
    <SectionMainFull title="Payment links">
      <BreadcrumbBase>
        <BreadcrumbContinue route="dashboard"> Dashboard </BreadcrumbContinue>
        <BreadcrumbEnd>Payment links</BreadcrumbEnd>
      </BreadcrumbBase>
      <CardBoxModal
        v-model="isCreatePaymentLinkModalActive"
        title="CREATE PAYMENT LINK"
        button="info"
        button-label=""
        action-label="confirm"
      >
        <div class="grid grid-cols-1 lg:grid-cols-1">
          <FormField label="Amount">
            <div>
              <div class="flex items-center gap-1 border border-[#ccc] rounded h-[45px] px-3">
                <span class="text-xs">ZMW</span>
                <input
                  v-model="formData.amount"
                  type="number"
                  class="border-none outline-none focus:ring-transparent w-full text-sm"
                />
              </div>
              <FormErrorMessage
                v-if="isInputInvalid.amount"
                message="Amount cannot be empty"
                color="#dc2626"
                textSize=""
              />
            </div>
          </FormField>
          <FormField label="Expiry date">
            <div>
              <VueDatePicker
                v-model="formData.expiryDate"
                :min-date="new Date()"
                :ui="{ input: 'h-[45px] mb-2' }"
                teleport-center
              />
              <FormErrorMessage
                message="Expiry date must be greater that the current date"
                v-if="isInputInvalid.expiryDate"
                color="#dc2626"
                textSize=""
              />
            </div>
          </FormField>
          <FormField label="Description">
            <div>
              <FormControl
                type="textarea"
                :maxlength="50"
                name="description"
                v-model="formData.description"
              />
            </div>
          </FormField>
        </div>
        <div class="flex mt-3">
          <BaseButton label="Create" color="info" outline @click="createPaymentLink" />
        </div>
      </CardBoxModal>

      <CardBoxModal
        v-model="isSharePaymentLinkModalActive"
        title="SHARE PAYMENT LINK"
        button="info"
        button-label=""
        action-label="confirm"
      >
        <div class="grid grid-cols-1 lg:grid-cols-1">
          <FormField label="Channel">
            <div>
              <DropdownSelector
                v-model="sharePaymentLinkFormData.channel"
                :options="channelOptions"
                placeholder="Select channel"
              />
            </div>
          </FormField>
          <FormField label="Receiver Number" v-if="sharePaymentLinkFormData.channel?.id == 'SMS'">
            <div>
              <FormControl
                type="number"
                name="receiver_number"
                v-model="sharePaymentLinkFormData.receiver_number"
                @change="validateSharePaymentLinkInputs"
              />
            </div>
          </FormField>
          <FormField label="Receiver Email" v-if="sharePaymentLinkFormData.channel?.id == 'EMAIL'">
            <div>
              <FormControl
                type="text"
                name="receiver_email"
                v-model="sharePaymentLinkFormData.receiver_email"
                placeholder="e.g <EMAIL>"
                @change="validateSharePaymentLinkInputs"
              />
            </div>
          </FormField>
          <FormField label="Purpose/Service">
            <div>
              <FormControl
                type="text"
                name="purpose"
                v-model="sharePaymentLinkFormData.purpose"
                placeholder="e.g loan repayment"
                @change="validateSharePaymentLinkInputs"
              />
            </div>
          </FormField>
        </div>
        <div class="flex mt-3">
          <BaseButton
            label="Share"
            color="info"
            outline
            @click="sharePaymentLink"
            :disabled="isSharePaymentLinkFormInValid"
          />
        </div>
      </CardBoxModal>
      <CardBoxModal
        v-model="isPaymentLinkDetailsModalActive"
        title=""
        button="info"
        button-label="Okay"
        action-label="ApproveItem"
      >
        <div>
          <div class="grid grid-cols-1 gap-3 mb-5 md:grid-cols-1">
            <div>
              <div
                class="p-1 rounded text-center"
                :class="
                  paymentLinkStatusColors(
                    isAfter(selectedData.expiry_date, new Date()) ? 'ACTIVE' : 'EXPIRED'
                  )
                "
              >
                {{ isAfter(selectedData.expiry_date, new Date()) ? 'ACTIVE' : 'EXPIRED' }}
              </div>
            </div>
          </div>
          <div class="grid grid-cols-1 gap-3 md:grid-cols-2">
            <div>
              <label class="block font-bold">External Ref</label>
              {{ selectedData.external_ref ?? 'N/A' }}
            </div>
          </div>
          <div class="grid grid-cols-1 gap-3 mt-2 md:grid-cols-2">
            <div>
              <label class="block font-bold">Amount</label>
              ZMW {{ selectedData.amount ?? '0' }}
            </div>
          </div>
          <div class="grid grid-cols-1 gap-3 mt-2 md:grid-cols-2">
            <div>
              <label class="block font-bold">Link</label>
              {{ selectedData.short_url ?? 'N/A' }}
            </div>
          </div>
          <div class="grid grid-cols-1 gap-3 md:grid-cols-1">
            <div>
              <label class="block font-bold">Description</label>
              {{ selectedData.description ?? 'N/A' }}
            </div>
          </div>
        </div>
      </CardBoxModal>

      <div class="grid grid-cols-1 mt-5 lg:grid-cols-1">
        <CardBox rounded="rounded-sm" class="mb-6 px-2" has-table style="overflow: auto">
          <SectionTitleLineWithButton :icon="mdiDomain" small>
            <BaseButton
              :icon="mdiPlus"
              label="Create"
              color="contrast"
              outline
              small
              @click="isCreatePaymentLinkModalActive = true"
            />
          </SectionTitleLineWithButton>
          <DatabaseLoaderOne v-if="mainLoader" />
          <NoRecordSvgVue v-else-if="items.length == 0 && mainLoader == false" />
          <table v-else>
            <thead class="text-xs">
              <tr>
                <th>DESCRIPTION</th>
                <th>AMOUNT</th>
                <th>CURRENCY</th>
                <th>LINK</th>
                <th>CREATED AT</th>
                <th>STATUS</th>
                <th>ACTION</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="data in itemsPaginated" :key="data.id" class="text-sm">
                <td>
                  <text-clamp :text="data.description ?? '-'" :max-lines="1" />
                </td>
                <td>{{ data.amount }}</td>
                <td>{{ data.currency }}</td>
                <td>
                  <div class="flex gap-1 items-center">
                    <text-clamp :text="data.short_url" :max-lines="1" />
                    <BaseIcon
                      @click="copy(data.short_url)"
                      :path="mdiClipboardOutline"
                      size="15"
                      w=""
                      h="h-10"
                      class="text-gray-600 px-2 cursor-pointer"
                    />
                  </div>
                </td>
                <td>{{ dayjs(data.created_at).format('YYYY-MM-DD HH:mm:ss') }}</td>
                <td>{{ isAfter(data.expiry_date, new Date()) ? 'ACTIVE' : 'EXPIRED' }}</td>
                <td>
                  <BaseButtons type="justify-start lg:justify-end" no-wrap>
                    <BaseButton
                      @click="viewPaymentLinkDetails(data.id)"
                      :icon="mdiEyeCircleOutline"
                      label="View"
                      color="contrast"
                      outline
                      small
                    />
                    <BaseButton
                      @click="sharePaymentLinkDetails(data.id)"
                      :icon="mdiEyeCircleOutline"
                      :disabled="isBefore(data.expiry_date, new Date())"
                      label="Share"
                      color="contrast"
                      outline
                      small
                    />
                  </BaseButtons>
                </td>
              </tr>
            </tbody>
          </table>

          <div
            class="p-3 lg:px-6 border-t border-gray-100 dark:border-slate-800"
            v-if="!mainLoader"
          >
            <BaseLevel>
              <BaseButtons>
                <!-- Start button -->
                <BaseButton
                  :label="'Start'"
                  :color="currentPage === 0 ? 'lightDark' : 'whiteDark'"
                  small
                  @click="goToPage(0)"
                />

                <!-- Previous button -->
                <BaseButton
                  :label="'Previous'"
                  :color="currentPage === 0 ? 'lightDark' : 'whiteDark'"
                  small
                  @click="previousPage"
                />

                <!-- Next button -->
                <BaseButton
                  :label="'Next'"
                  :color="currentPage === numPages - 1 ? 'lightDark' : 'whiteDark'"
                  small
                  @click="nextPage"
                />

                <!-- End button -->
                <BaseButton
                  :label="'End'"
                  :color="currentPage === numPages - 1 ? 'lightDark' : 'whiteDark'"
                  small
                  @click="goToPage(numPages - 1)"
                />
              </BaseButtons>
              <small>Page {{ currentPageHuman }} of {{ numPages }}</small>
            </BaseLevel>
          </div>
        </CardBox>
      </div>

      <Loading v-model="loading" />
    </SectionMainFull>
  </LayoutAuthenticated>
</template>
<script setup>
import BaseButton from '@/components/BaseButton.vue'
import SectionMainFull from '@/components/SectionMainFull.vue'
import SectionTitleLineWithButton from '@/components/SectionTitleLineWithButton.vue'
import LayoutAuthenticated from '@/layouts/LayoutAuthenticated.vue'
import { computed, onMounted, ref, watch } from 'vue'
import { mdiPlus, mdiClipboardOutline } from '@mdi/js'
import { useAuthStore } from '@/stores/auth'
import { usePaymentLinksStore } from '@/stores/paymentLinks'
import dayjs from 'dayjs'
import BaseLevel from '@/components/BaseLevel.vue'
import BaseButtons from '@/components/BaseButtons.vue'
import CardBoxModal from '@/components/CardBoxModal.vue'
import FormField from '@/components/FormField.vue'
import FormControl from '@/components/FormControl.vue'
import FormErrorMessage from '@/components/FormErrorMessage.vue'
import { paymentLinkStatusColors } from '@/helpers/functions/status.js'
import { addDays, differenceInSeconds, isAfter, isBefore } from 'date-fns'
import sleep from 'sleep-promise'
import DatabaseLoaderOne from '@/components/Reusables/Loaders/DatabaseLoaderOne.vue'
import NoRecordSvgVue from '@/components/Reusables/Svgs/NoRecordSvg.vue'
import { useSuccess, useWarning } from '@/helpers/functions/notifications'
import BreadcrumbBase from '@/components/Reusables/Breadcrumbs/BreadcrumbBase.vue'
import BreadcrumbContinue from '@/components/Reusables/Breadcrumbs/BreadcrumbContinue.vue'
import BreadcrumbEnd from '@/components/Reusables/Breadcrumbs/BreadcrumbEnd.vue'
import { useClipboard } from '@vueuse/core'
import { usePush } from 'notivue'
import BaseIcon from '@/components/BaseIcon.vue'
import DropdownSelector from '@/components/DropdownSelector.vue'
import Loading from '@/components/modals/Loading.vue'

const successPush = useSuccess()
const push = usePush()
const warningPush = useWarning()
const { copy, copied } = useClipboard()

const mainLoader = ref(true)
const loading = ref(false)

const isCreatePaymentLinkModalActive = ref(false)
const isSharePaymentLinkModalActive = ref(false)
const isPaymentLinkDetailsModalActive = ref(false)
const selectedData = ref({})
const perPage = ref(5)
const currentPage = ref(0)

const channelOptions = ref([
  {
    name: 'SMS',
    id: 'SMS'
  },
  {
    name: 'Email',
    id: 'EMAIL'
  }
])

watch(copied, () => {
  if (copied.value) {
    push.success({ message: 'Link copied', duration: 1000 })
  }
})

const formData = ref({
  description: '',
  expiryDate: addDays(new Date(), 1),
  amount: ''
})

const sharePaymentLinkFormData = ref({
  channel: null,
  receiver_email: '',
  receiver_number: '',
  purpose: ''
})

const isSharePaymentLinkDataInValid = ref({
  channel: true,
  receiver_email: true,
  receiver_number: true
})

const isInputInvalid = ref({
  description: false,
  expiryDate: false,
  amount: false,
  currency: false
})

const authStore = useAuthStore()
const paymentLinksStore = usePaymentLinksStore()

const accountData = computed(() => {
  return authStore.accountData
})

watch(sharePaymentLinkFormData.value, () => {
  if (!sharePaymentLinkFormData.value.channel) {
    isSharePaymentLinkDataInValid.value.channel = true
  } else {
    isSharePaymentLinkDataInValid.value.channel = false
  }

  if (
    sharePaymentLinkFormData.value.channel?.id == 'SMS' &&
    !sharePaymentLinkFormData.value.receiver_number
  ) {
    isSharePaymentLinkDataInValid.value.receiver_number = true
  } else {
    isSharePaymentLinkDataInValid.value.receiver_number = false
  }

  if (
    sharePaymentLinkFormData.value.channel?.id == 'EMAIL' &&
    !sharePaymentLinkFormData.value.receiver_email
  ) {
    isSharePaymentLinkDataInValid.value.receiver_email = true
  } else {
    isSharePaymentLinkDataInValid.value.receiver_email = false
  }

  if (!sharePaymentLinkFormData.value.purpose) {
    isSharePaymentLinkDataInValid.value.purpose = true
  } else {
    isSharePaymentLinkDataInValid.value.purpose = false
  }
})

const items = computed(() => {
  return Array.isArray(paymentLinksStore.links) ? paymentLinksStore.links : []
})

const isFormValid = computed(() => {
  return (
    !isInputInvalid.value.description &&
    !isInputInvalid.value.expiryDate &&
    !isInputInvalid.value.amount
  )
})

const isSharePaymentLinkFormInValid = computed(() => {
  return (
    isSharePaymentLinkDataInValid.value.channel ||
    isSharePaymentLinkDataInValid.value.receiver_email ||
    isSharePaymentLinkDataInValid.value.receiver_number
  )
})

const itemsPaginated = computed(() =>
  items.value.slice(perPage.value * currentPage.value, perPage.value * (currentPage.value + 1))
)

const numPages = computed(() => Math.ceil(items.value.length / perPage.value))
const currentPageHuman = computed(() => currentPage.value + 1)

const goToPage = (page) => {
  currentPage.value = page
}

const previousPage = () => {
  if (currentPage.value > 0) {
    currentPage.value -= 1
  }
}

const nextPage = () => {
  if (currentPage.value < numPages.value - 1) {
    currentPage.value += 1
  }
}

const validateInputs = (expirySeconds) => {
  if (!formData.value.amount) {
    isInputInvalid.value.amount = true
  } else {
    isInputInvalid.value.amount = false
  }

  if (isNaN(expirySeconds) || expirySeconds < 60) {
    isInputInvalid.value.expiryDate = true
  } else {
    isInputInvalid.value.expiryDate = false
  }
}

const createPaymentLink = async () => {
  try {
    var expirySeconds = differenceInSeconds(formData.value.expiryDate, new Date())

    validateInputs(expirySeconds)

    if (!isFormValid.value) {
      return
    }

    var payload = {
      client_id: accountData.value.id,
      description: formData.value.description,
      amount: formData.value.amount,
      expirySeconds
    }

    isCreatePaymentLinkModalActive.value = false
    mainLoader.value = true
    var resp = await paymentLinksStore.createPaymentLink(payload)

    if (resp == 201) {
      successPush('Payment link created')
      await paymentLinksStore.getPaymentLinks(accountData.value.id)
    } else {
      isCreatePaymentLinkModalActive.value = false
      warningPush('Something went wrong', 'Please try again.')
    }
  } catch (error) {
    isCreatePaymentLinkModalActive.value = true
    warningPush('Something went wrong', 'Please try again.')
  } finally {
    mainLoader.value = false
  }
}

const sharePaymentLink = async () => {
  try {
    var payload = {
      plink_id: selectedData.value.unique_id,
      receiver_email: sharePaymentLinkFormData.value.receiver_email,
      receiver_number: sharePaymentLinkFormData.value.receiver_number.toString(),
      channel: sharePaymentLinkFormData.value.channel.id,
      purpose: sharePaymentLinkFormData.value.purpose
    }

    loading.value = true

    var resp = await paymentLinksStore.sharePaymentLink(payload)

    if (resp == 200) {
      successPush('Payment link shared')
    } else {
      warningPush('Something went wrong', 'Please try again.')
    }
  } catch (error) {
    warningPush('Something went wrong', 'Please try again.')
  } finally {
    loading.value = false
  }
}

const viewPaymentLinkDetails = (id) => {
  selectedData.value = items.value.find((item) => item.id == id)
  isPaymentLinkDetailsModalActive.value = true
}

const sharePaymentLinkDetails = (id) => {
  selectedData.value = items.value.find((item) => item.id == id)
  isSharePaymentLinkModalActive.value = true
}

onMounted(async () => {
  try {
    await paymentLinksStore.getPaymentLinks(accountData.value.id)
  } catch (e) {
  } finally {
    mainLoader.value = false
  }
})
</script>
