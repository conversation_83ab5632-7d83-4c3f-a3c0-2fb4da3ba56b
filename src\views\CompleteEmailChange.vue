<template>
  <div class="h-screen flex justify-center items-center">
    <half-circle-spinner v-if="loading" :animation-duration="1000" :size="60" color="#EC3490" />
    <PaymentLinkNotFound v-if="!loading && error" :message="error" />
    <h1 v-if="!loading && !error">Your email has been successfully updated</h1>
  </div>
</template>
<script setup>
import { HalfCircleSpinner } from 'epic-spinners'
import { onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import PaymentLinkNotFound from '@/components/PaymentLinkNotFound.vue'
import { useAuthStore } from '@/stores/auth'

const route = useRoute()
const loading = ref(true)
const error = ref('')
const authStore = useAuthStore()

onMounted(async () => {
  try {
    var token = route.query?.token

    var resp = await authStore.completeEmailChange(token)

    if (resp.status != 200) {
      loading.value = false
      error.value = 'Your email change request as been declined'
    }
  } finally {
    loading.value = false
  }
})
</script>
