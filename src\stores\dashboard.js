import { defineStore } from 'pinia'
import { use<PERSON>out<PERSON>, useRouter } from 'vue-router'
import { BASE_URL, api } from '@/helpers/api/http-commons.js'
import axios from 'axios'

export const useDashboardStore = defineStore('dashboard', {
  state: () => {
    return {
      dailyStats: [],
      monthlyStats: [],
      dailyCompanyStats: [],
      monthlyCompanyStats: []
    }
  },
  getters: {},
  actions: {
    async getDailyClientStats(id) {
      try {
        let config = {
          method: 'get',
          maxBodyLength: Infinity,
          url: BASE_URL + api.DASHBOARD.BASE + api.DASHBOARD.GET_DAILY_CLIENTS_STATS + id,
          headers: {
            'Content-Type': 'application/json'
          }
        }
        const response = await axios.request(config)
        this.dailyStats = response.data
        return response.status
      } catch (error) {
        return error.response.status
      }
    },
    async getMonthlyClientStats(id) {
      try {
        let config = {
          method: 'get',
          maxBodyLength: Infinity,
          url: BASE_URL + api.DASHBOARD.BASE + api.DASHBOARD.GET_MONTHLY_CLIENTS_STATS + id,
          headers: {
            'Content-Type': 'application/json'
          }
        }
        const response = await axios.request(config)
        this.monthlyStats = response.data
        return response.status
      } catch (error) {
        return error.response.status
      }
    },
    async getDailyCompanyStats(id) {
      try {
        let config = {
          method: 'get',
          maxBodyLength: Infinity,
          url: BASE_URL + api.DASHBOARD.BASE + api.DASHBOARD.GET_DAILY_COMPANY_STATS + id,
          headers: {
            'Content-Type': 'application/json'
          }
        }
        const response = await axios.request(config)
        this.dailyCompanyStats = response.data
        return response.status
      } catch (error) {
        return error.response.status
      }
    },
    async getDailyPrimeNetStats() {
      try {
        let config = {
          method: 'get',
          maxBodyLength: Infinity,
          url: BASE_URL + api.DASHBOARD.BASE + api.DASHBOARD.GET_DAILY_PRIMENET_STATS,
          headers: {
            'Content-Type': 'application/json'
          }
        }
        const response = await axios.request(config)
        this.dailyCompanyStats = response.data
        return response.status
      } catch (error) {
        return error.response.status
      }
    },
    async getMonthlyPrimeNetStats() {
      try {
        let config = {
          method: 'get',
          maxBodyLength: Infinity,
          url: BASE_URL + api.DASHBOARD.BASE + api.DASHBOARD.GET_MONTHLY_PRIMENET_STATS,
          headers: {
            'Content-Type': 'application/json'
          }
        }
        const response = await axios.request(config)
        this.monthlyCompanyStats = response.data
        return response.status
      } catch (error) {
        return error.response.status
      }
    },
    async getMonthlyCompanyStats(id) {
      try {
        let config = {
          method: 'get',
          maxBodyLength: Infinity,
          url: BASE_URL + api.DASHBOARD.BASE + api.DASHBOARD.GET_MONTHLY_COMPANY_STATS + id,
          headers: {
            'Content-Type': 'application/json'
          }
        }
        const response = await axios.request(config)
        this.monthlyStats = response.data
        return monthlyCompanyStats.status
      } catch (error) {
        return error.response.status
      }
    }
  }
})
