<template>
  <LayoutAuthenticated>
    <SectionMainFull>
      <SectionTitleLineWithButton :icon="mdiCashClock" title="Accounts Summary" small>
      </SectionTitleLineWithButton>
      <div class="mb-8 bg-white p-4 shadow">
        <div class="grid grid-cols-1 lg:grid-cols-3 lg:gap-1 mb-3">
          <FormField label="Account">
            <div>
              <multiselect
                v-model="client"
                :options="clientsData"
                label="business_name"
                placeholder="Select account"
                height="60px"
              >
              </multiselect>
            </div>
          </FormField>
          <FormField label="Service">
            <div>
              <multiselect
                v-model="service"
                :options="servicesOptions"
                label="name"
                placeholder="Select service"
              >
              </multiselect>
            </div>
          </FormField>
          <FormField label="Start date">
            <div>
              <VueDatePicker
                v-model="formData.startDate"
                :ui="{ input: 'h-[40px] mb-2' }"
                teleport-center
              />
              <FormErrorMessage
                message="Start date must be greater that the current date"
                v-if="isInputInvalid.startDate"
                color="#dc2626"
                textSize=""
              />
            </div>
          </FormField>
          <FormField label="End date">
            <div>
              <VueDatePicker
                v-model="formData.endDate"
                :max-date="new Date()"
                :ui="{ input: 'h-[40px] mb-2' }"
                teleport-center
              />
              <FormErrorMessage
                message="Start date must be greater that the current date"
                v-if="isInputInvalid.startDate"
                color="#dc2626"
                textSize=""
              />
            </div>
          </FormField>

          <FormField label="Merchant ID">
            <div>
              <FormControl v-model="formData.account_number" type="text" name="account_number" />
            </div>
          </FormField>
        </div>

        <div class="flex gap-2">
          <BaseButton label="View" color="info" @click="() => getAccountsSummary()" />
          <BaseButton label="Export" outline color="info" @click="exportAccountsSummary" />
        </div>

        <div class="mt-4 flex items-center gap-2">
          <input
            type="checkbox"
            v-model="arrange_by_account_number"
            id="arrange_by_account_number"
          />
          <label for="arrange_by_account_number" class="text-sm">Arrange By Merchant ID</label>
        </div>
      </div>
      <div class="flex justify-end mb-3" v-if="summaries.length">
        <BaseButton small label="Export" color="info" @click="exportAccountsSummary" />
      </div>

      <table v-if="summaries.length && resultType == 'ACT_SUM'">
        <thead class="text-sm">
          <tr>
            <th colspan="1">Account Name</th>
            <th colspan="2">Successfull Payments</th>
            <th colspan="2">Failed Payments</th>
            <th colspan="2">Total payments</th>
          </tr>
          <tr class="text-gray-600">
            <th scope="col"></th>
            <th scope="col">No of Payments</th>
            <th scope="col">Payments Amount(ZMW)</th>
            <th scope="col">No of Payments</th>
            <th scope="col">Payments Amount (ZMW)</th>
            <th scope="col">Total No of Payments</th>
            <th scope="col">Total Amount of Payments (ZMW)</th>
          </tr>
        </thead>
        <tbody class="text-sm">
          <tr v-for="summary in summaries">
            <td>{{ summary.client.business_name }}</td>
            <td>{{ summary.successfulCount }}</td>
            <td>{{ summary.successfulAmount }}</td>
            <td>{{ summary.unsuccessfulCount }}</td>
            <td>{{ summary.unsuccessfulAmount }}</td>
            <td>{{ summary.totalCount }}</td>
            <td>{{ summary.totalAmount }}</td>
          </tr>
        </tbody>
      </table>

      <table v-if="summaries.length && resultType != 'ACT_SUM'">
        <thead class="text-sm">
          <tr>
            <th colspan="1">Account Name</th>
            <th colspan="1">Merchant ID</th>
            <th colspan="2">Successfull Payments</th>
            <th colspan="2">Failed Payments</th>
            <th colspan="2">Total payments</th>
          </tr>
          <tr class="text-gray-600">
            <th scope="col"></th>
            <th scope="col"></th>
            <th scope="col">No of Payments</th>
            <th scope="col">Payments Amount(ZMW)</th>
            <th scope="col">No of Payments</th>
            <th scope="col">Payments Amount (ZMW)</th>
            <th scope="col">Total No of Payments</th>
            <th scope="col">Total Payments (ZMW)</th>
          </tr>
        </thead>
        <tbody class="text-sm">
          <tr v-for="summary in summaries">
            <td>{{ summary.client.business_name }}</td>
            <td>{{ summary.account_number }}</td>
            <td>{{ summary.successfulCount }}</td>
            <td>{{ summary.successfulAmount }}</td>
            <td>{{ summary.unsuccessfulCount }}</td>
            <td>{{ summary.unsuccessfulAmount }}</td>
            <td>{{ summary.totalCount }}</td>
            <td>{{ summary.totalAmount }}</td>
          </tr>
        </tbody>
      </table>

      <div
        class="p-3 lg:px-6 border-t border-gray-100 dark:border-slate-800"
        v-if="summaries.length && totalPages > 1"
      >
        <vue-awesome-paginate
          :total-items="totalResults"
          :items-per-page="limit"
          :max-pages-shown="5"
          v-model="currentPage"
          @click="paginate"
        />
      </div>

      <p v-if="!summaries.length && searched" class="text-center">No results found</p>

      <Loading v-model="isLoading" />
    </SectionMainFull>
  </LayoutAuthenticated>
</template>
<script setup>
import BaseButton from '@/components/BaseButton.vue'
import CardBox from '@/components/CardBox.vue'
import FormControl from '@/components/FormControl.vue'
import FormErrorMessage from '@/components/FormErrorMessage.vue'
import FormField from '@/components/FormField.vue'
import Loading from '@/components/modals/Loading.vue'
import SectionMainFull from '@/components/SectionMainFull.vue'
import SectionTitleLineWithButton from '@/components/SectionTitleLineWithButton.vue'
import LayoutAuthenticated from '@/layouts/LayoutAuthenticated.vue'
import sleep from 'sleep-promise'
import Multiselect from 'vue-multiselect'
import { useReportsStore } from '@/stores/reports'
import { formatISO } from 'date-fns'
import { computed, ref, watch } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { api, BASE_URL } from '@/helpers/api/http-commons'
import { usePush } from 'notivue'

const formData = ref({
  startDate: new Date(),
  endDate: new Date(),
  account_number: ''
})

const push = usePush()

const summaries = ref([])
const client = ref({
  business_name: 'All',
  id: 0
})
const service = ref({
  name: 'All',
  id: 0
})
const isLoading = ref(false)
const searched = ref(false)
const arrange_by_account_number = ref(false)
const currentPage = ref(1)
const limit = ref(10)
const totalResults = ref(1)
const totalPages = ref(1)
const resultType = ref('ACT_SUM')

const servicesOptions = ref([
  {
    name: 'All',
    id: 0
  },
  {
    name: 'Airtel',
    id: 1
  },
  {
    name: 'MTN',
    id: 2
  },
  {
    name: 'Zamtel',
    id: 3
  }
])

const isInputInvalid = ref({
  description: false,
  startDate: false,
  amount: false,
  currency: false
})

const reportsStore = useReportsStore()
const auth = useAuthStore()

const clientsData = computed(() => {
  return [{ business_name: 'All', id: 0 }, ...auth.clientsData]
})

const getAccountsSummary = async (page = 1, action = 'submit') => {
  const startDate = formatISO(formData.value.startDate).split('+')[0]
  const endDate = formatISO(formData.value.endDate).split('+')[0]

  isLoading.value = true
  searched.value = false

  const response = await reportsStore.getAccountsSummary({
    start_date: startDate,
    end_date: endDate,
    client_id: client.value?.id,
    service_id: service.value?.id,
    account_number: formData.value.account_number,
    arrange_by_account_number: arrange_by_account_number.value,
    page,
    limit: limit.value
  })

  if (response.status == 200) {
    summaries.value = response.data.summaries
    totalPages.value = response.data.total_pages
    totalResults.value = response.data.total_results

    searched.value = true

    if (action == 'submit') {
      currentPage.value = 1
    }

    if (!arrange_by_account_number.value && !formData.value.account_number) {
      resultType.value = 'ACT_SUM'
    } else {
      resultType.value = 'ACT_SUM_WITH_ACT_NO'
    }
  } else {
    push.error({ message: 'Something went wrong', duration: 1000 })
  }

  isLoading.value = false
}

watch(currentPage, () => {
  if (isLoading.value) {
    return
  }
  getAccountsSummary(currentPage.value, 'paginate')
})

const exportAccountsSummary = () => {
  const startDate = formatISO(formData.value.startDate).split('+')[0]
  const endDate = formatISO(formData.value.endDate).split('+')[0]

  var queryStrings = `?start_date=${startDate}&end_date=${endDate}&export=true`

  if (formData.value.account_number) {
    queryStrings += `&account_number=${formData.value.account_number}`
  }

  if (client.value) {
    queryStrings += `&client_id=${client.value?.id}`
  }

  if (formData.service_id) {
    queryStrings += `&service_id=${formData.service_id}`
  }

  if (arrange_by_account_number.value) {
    queryStrings += `&arrange_by_account_number=${arrange_by_account_number.value}`
  }

  window.open(
    `${BASE_URL + api.REPORTS.BASE_URL + api.REPORTS.ACCOUNTS_SUMMARIES + queryStrings}`,
    '_self'
  )
}
</script>

<style src="vue-multiselect/dist/vue-multiselect.css"></style>

<style>
.multiselect__option--highlight {
  background: #eee !important;
  outline: none;
  color: black;
}

.multiselect__option--highlight::after {
  content: attr(data-select);
  background: #eee !important;
  color: black;
}

.multiselect__input:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.125);
}

.pagination-container {
  display: flex;

  column-gap: 10px;
}

.paginate-buttons {
  height: 40px;

  width: 40px;

  border-radius: 10px;

  cursor: pointer;

  background-color: rgb(242, 242, 242);

  border: 1px solid rgb(217, 217, 217);

  color: black;
}

.paginate-buttons:hover {
  background-color: #d8d8d8;
}

.active-page {
  background-color: #3498db;

  border: 1px solid #3498db;

  color: white;
}

.active-page:hover {
  background-color: #2988c8;
}
</style>
