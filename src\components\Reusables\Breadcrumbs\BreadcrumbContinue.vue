<template>
  <router-link :to="{name:props.route}" class="text-lg">
    <div class="flex items-center text-gray-400">
    <div class="pb-1">
      <slot />
    </div>
      <BaseIcon :path="mdiChevronRight" size="30" w="" h="h-10"
        class="text-gray-400 h-full"
 />
    </div>
  </router-link>
</template>
<script setup>
import BaseIcon from "@/components/BaseIcon.vue";
import {
  mdiChevronRight
} from "@mdi/js";
const props = defineProps({
  route: {
    type: String,
    required: false,
    default: ""
  }
})
</script>
