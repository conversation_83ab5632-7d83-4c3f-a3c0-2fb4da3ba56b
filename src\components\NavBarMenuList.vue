<script setup>
import NavBarItem from '@/components/NavBarItem.vue'
import { logout } from "@/helpers/functions/auth.js";

defineProps({
  menu: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['menu-click'])

const menuClick = (event, item) => {
  emit('menu-click', event, item)
  if(item.label == "Logout"){
    logout();
  }
}
</script>

<template>
  <NavBarItem v-for="(item, index) in menu" :key="index" :item="item" @menu-click="menuClick" />
</template>
