import { defineStore } from 'pinia'
import { use<PERSON>out<PERSON>, useRouter } from 'vue-router'
import { BASE_URL, api } from '@/helpers/api/http-commons.js'
import axios from 'axios'

export const useCompanyStore = defineStore('company', {
  state: () => {
    return {
      users: [],
      total_users: 0,
      limit: 20
    }
  },
  getters: {},
  actions: {
    async getCompanyUsers(id, page = 1) {
      try {
        let config = {
          method: 'get',
          maxBodyLength: Infinity,
          url: `${BASE_URL + api.COMPANY.GET_COMPANY_USERS + id}?page=${page}&limit=${this.limit}`,
          headers: {
            'Content-Type': 'application/json'
          }
        }
        const response = await axios.request(config)

        this.users = response.data.data
        this.total_users = response.data.total_users
        return response.status
      } catch (error) {
        return error.response.status
      }
    },
    async assignUserAccount(data) {
      try {
        let config = {
          method: 'post',
          maxBodyLength: Infinity,
          url: BASE_URL + api.COMPANY.GET_COMPANY_USERS,
          headers: {
            'Content-Type': 'application/json'
          },
          data: data
        }
        const response = await axios.request(config)

        this.users = response.data.data
        return response.status
      } catch (error) {
        return error.response.status
      }
    }
  }
})
