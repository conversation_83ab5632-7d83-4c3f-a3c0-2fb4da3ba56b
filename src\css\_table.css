@layer base {
  table {
    @apply w-full;
  }

  thead {
    @apply hidden lg:table-header-group;
  }

  tr {
    @apply max-w-full block relative border-b-4 border-gray-100
      lg:table-row lg:border-b-0 dark:border-slate-800;
  }

  tr:last-child {
    @apply border-b-0;
  }

  td:not(:first-child) {
    @apply lg:dark:border-slate-700;
  }

  th {
    @apply lg:text-left lg:p-3;
  }

  td {
    @apply flex justify-between text-right py-3 px-4 align-top border-b border-gray-100
      lg:table-cell lg:text-left lg:p-3 lg:align-middle lg:border-b-0 dark:border-slate-800;
  }

  td:last-child {
    @apply border-b-0;
  }

  tbody tr,
  tbody tr:nth-child(odd) {
    @apply lg:hover:bg-gray-100 lg:dark:hover:bg-slate-700/70;
  }

  tbody tr:nth-child(odd) {
    @apply lg:bg-gray-100/50 lg:dark:bg-slate-800/50;
  }

  td:before {
    content: attr(data-label);
    @apply font-semibold pr-3 text-left lg:hidden;
  }
  th, td {
    text-align: center;
  }
  table, td, th {
    border: 1px solid rgb(222, 226, 230);
  }

}
