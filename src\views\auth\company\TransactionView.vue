<template>
  <LayoutAuthenticated title="Transaction Detail">
    <SectionMainFull>
      <DatabaseLoaderOne v-if="loading" />

      <div class="max-w-3xl mx-auto p-8 bg-white shadow-[0_2px_10px_-3px_rgba(6,81,237,0.3)]" v-else>
        <CardBox
          rounded="rounded-sm"
          class="mb-6 px-2"
          has-table
          style="overflow: auto"
          v-if="transaction"
        >
          <div class="flex justify-between mb-2">
            <button
              v-if="authUserData.role == 'SUPERADMIN'"
              class="bg-[#090133] px-3 py-1 rounded text-gray-200"
              @click="isReconcileTModalActive = true"
            >
              Reconcile Transaction
            </button>
            <button v-if="authUserData.role == 'SUPERADMIN' && transaction.status_code == 300"
            class="bg-[#17a2b8] px-3 py-1 rounded text-white" @click="startRefundModal(true)">
            Refund Transaction
          </button>
          </div>
          <table class="text-sm mt-1">
            <tbody class="text-left">
              <tr class="flex">
                <td class="w-[15rem] font-[500] text-[#333] text-sm">TRANSACTION ID</td>
                <td class="flex-grow">{{ transaction.transaction_id ?? '-' }}</td>
              </tr>
              <tr class="flex">
                <td class="w-[15rem] font-[500] text-[#333] text-sm">TRANSACTION TYPE</td>
                <td class="flex-grow">{{ transaction.transaction_type ?? '-' }}</td>
              </tr>
              <tr class="flex">
                <td class="w-[15rem] font-[500] text-[#333] text-sm">CLIENT NAME</td>
                <td class="flex-grow">{{ transaction.client_name ?? '-' }}</td>
              </tr>
              <tr class="flex">
                <td class="w-[15rem] font-[500] text-[#333] text-sm">SERVICE NAME</td>
                <td class="flex-grow">{{ transaction.service_name ?? '-' }}</td>
              </tr>
              <tr class="flex">
                <td class="w-[15rem] font-[500] text-[#333] text-sm">EXTERNAL ID</td>
                <td class="flex-grow">{{ transaction.external_id ?? '-' }}</td>
              </tr>
              <tr class="flex">
                <td class="w-[15rem] font-[500] text-[#333] text-sm">TRANSACTION AMOUNT</td>
                <td class="flex-grow">{{ transaction.amount ?? '-' }}</td>
              </tr>
              <tr class="flex">
                <td class="w-[15rem] font-[500] text-[#333] text-sm">ACCOUNT NUMBER</td>
                <td class="flex-grow">{{ transaction.account_number ?? '-' }}</td>
              </tr>
              <tr class="flex">
                <td class="w-[15rem] font-[500] text-[#333] text-sm">PHONE NUMBER</td>
                <td class="flex-grow">{{ transaction.phone_number ?? '-' }}</td>
              </tr>
              <tr class="flex">
                <td class="w-[15rem] font-[500] text-[#333] text-sm">MERCHANT ID</td>
                <td class="flex-grow">{{ transaction.merchant_id ?? '-' }}</td>
              </tr>
              <tr class="flex">
                <td class="w-[15rem] font-[500] text-[#333] text-sm">NARRATION</td>
                <td class="flex-grow">{{ transaction.narration ?? '-' }}</td>
              </tr>
              <tr class="flex">
                <td class="w-[15rem] font-[500] text-[#333] text-sm">CURRENCY</td>
                <td class="flex-grow">{{ transaction.currency ?? '-' }}</td>
              </tr>
              <tr class="flex">
                <td class="w-[15rem] font-[500] text-[#333] text-sm">STATUS</td>
                <td class="flex-grow">{{ statusCodeToName(transaction.status_code) }}</td>
              </tr>
              <tr class="flex">
                <td class="w-[15rem] font-[500] text-[#333] text-sm">RESPONSE MESSAGE</td>
                <td class="flex-grow">{{ transaction.response_message ?? '-' }}</td>
              </tr>
              <tr class="flex">
                <td class="w-[15rem] font-[500] text-[#333] text-sm">TRANSACTION DATE</td>
                <td class="flex-grow">
                  {{ dayjs(transaction.created_at).format('YYYY-MM-DD HH:mm:ss') }}
                </td>
              </tr>
              <tr class="flex">
                <td class="w-[15rem] font-[500] text-[#333] text-sm">DATE MODIFIED</td>
                <td class="flex-grow">
                  {{ dayjs(transaction.updated_at).format('YYYY-MM-DD HH:mm:ss') }}
                </td>
              </tr>
            </tbody>
          </table>
        </CardBox>
      </div>

      <!-- MODALS -->

      <ReconcileTransaction v-model="isReconcileTModalActive" :transaction="transaction" />
      <CardBoxModal
      v-model="isRefundModalActive"
      title="Refund Transaction"
      button="info"
      button-label=""
      action-label="confirm"
    >
      <RefundTransaction @refund-modal-emit="refundModalEmit" :transaction="transaction" />
    </CardBoxModal>
    </SectionMainFull>
  </LayoutAuthenticated>
</template>
<script setup>
import DatabaseLoaderOne from '@/components/Reusables/Loaders/DatabaseLoaderOne.vue'
import SectionMainFull from '@/components/SectionMainFull.vue'
import CardBox from '@/components/CardBox.vue'
import CardBoxModal from '@/components/CardBoxModal.vue'
import LayoutAuthenticated from '@/layouts/LayoutAuthenticated.vue'
import { computed, onMounted, ref } from 'vue'
import { useTransactionStore } from '@/stores/transactions'
import { useRoute, useRouter } from 'vue-router'
import dayjs from 'dayjs'
import ReconcileTransaction from '@/components/modals/ReconcileTransaction.vue'
import RefundTransaction from '@/components/modals/Transactions/RefundTransaction.vue'
import { useAuthStore } from '@/stores/auth'
import { statusCodeToName } from '@/helpers/functions/status'
import BaseButton from '@/components/BaseButton.vue'
import { mdiArrowLeft } from '@mdi/js'

const isReconcileTModalActive = ref(false)
const isRefundModalActive = ref(false);

var transactionStore = useTransactionStore()
const authStore = useAuthStore()

const route = useRoute()
const router = useRouter()
var loading = ref(true)

const goBack = () => {
  router.go(-1)
}

const transaction = computed(() => {
  return transactionStore.transaction
})

const authUserData = computed(() => {
  return authStore.userData
})

const startRefundModal = (status) => {
  isRefundModalActive.value = status;
}

const refundModalEmit = (event, item) => {
  startRefundModal(false);
}

onMounted(async () => {
  await transactionStore.getTransaction(route.params.id)
  loading.value = false
})
</script>
