import { defineStore } from 'pinia'
import { BASE_URL, api } from '@/helpers/api/http-commons.js'
import axios from 'axios'

export const useReconsStore = defineStore('recons', {
  state: () => {
    return {
      configs: {},
      recons: {},
      recon: {},
      summary: {},
      limit: 5
    }
  },
  getters: {},
  actions: {
    async getRecons(page, recon_type) {
      try {
        let config = {
          method: 'get',
          maxBodyLength: Infinity,
          url:
            BASE_URL +
            api.RECONS.BASE_URL +
            `?page=${page ?? 1}&limit=${this.limit}&recon_type=${recon_type}`,
          headers: {
            'Content-Type': 'application/json'
          }
        }

        const response = await axios.request(config)
        this.recons = response.data

        return response
      } catch (error) {
        return error.response
      }
    },
    async getFilteredRecons(data) {
      try {
        let config = {
          method: 'post',
          maxBodyLength: Infinity,
          url: BASE_URL + api.RECONS.BASE_URL + api.RECONS.FILTERED,
          headers: {
            'Content-Type': 'application/json'
          },
          data: data
        }

        const response = await axios.request(config)
        this.recons = response.data
        return response
      } catch (error) {
        return error.response
      }
    },
    async updateReconsStatus(data) {
      try {
        let config = {
          method: 'patch',
          maxBodyLength: Infinity,
          url: BASE_URL + api.RECONS.BASE_URL + api.RECONS.UPDATE_RECONS_STATUS,
          headers: {
            'Content-Type': 'application/json'
          },
          data: data
        }

        const response = await axios.request(config)
        return response
      } catch (error) {
        return error.response
      }
    },
    async exportRecons(data) {
      try {
        let config = {
          method: 'post',
          maxBodyLength: Infinity,
          url: BASE_URL + api.RECONS.BASE_URL + api.RECONS.EXPORT,
          responseType: 'blob',
          headers: {
            'Content-Type': 'application/json'
          },
          params: {},
          data: data
        }
        return await axios.request(config)
      } catch (error) {
        return error.response
      }
    },
    async getRecon(id) {
      try {
        let config = {
          method: 'get',
          maxBodyLength: Infinity,
          url: BASE_URL + api.RECONS.BASE_URL + '/' + id,
          headers: {
            'Content-Type': 'application/json'
          }
        }

        const response = await axios.request(config)
        this.recon = response.data
        return response
      } catch (error) {
        return error.response
      }
    },
    async updateSettlementStatus(data) {
      try {
        let config = {
          method: 'patch',
          maxBodyLength: Infinity,
          url: BASE_URL + api.RECONS.BASE_URL + '/' + data.id,
          data: { status: data.status },
          headers: {
            'Content-Type': 'application/json'
          }
        }

        const response = await axios.request(config)
        return response
      } catch (error) {
        return error.response
      }
    },
    async getConfigs(page) {
      try {
        let config = {
          method: 'get',
          maxBodyLength: Infinity,
          url:
            BASE_URL +
            api.RECONS.BASE_URL +
            api.RECONS.CONFIGS +
            `?page=${page ?? 1}&limit=${this.limit}`,
          headers: {
            'Content-Type': 'application/json'
          }
        }

        const response = await axios.request(config)
        this.configs = response.data
        return response
      } catch (error) {
        return error.response
      }
    },
    async addConfig(data) {
      try {
        let config = {
          method: 'post',
          maxBodyLength: Infinity,
          url: BASE_URL + api.RECONS.BASE_URL + api.RECONS.CONFIGS,
          data: data,
          headers: {
            'Content-Type': 'application/json'
          }
        }

        const response = await axios.request(config)
        return response
      } catch (error) {
        return error.response
      }
    },
    async updateConfig(data, id) {
      try {
        let config = {
          method: 'put',
          maxBodyLength: Infinity,
          url: BASE_URL + api.RECONS.BASE_URL + api.RECONS.CONFIGS + '/' + id,
          data: data,
          headers: {
            'Content-Type': 'application/json'
          }
        }

        const response = await axios.request(config)
        return response
      } catch (error) {
        return error.response
      }
    }
  }
})
