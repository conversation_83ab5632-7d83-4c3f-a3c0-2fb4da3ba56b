import { defineStore } from 'pinia'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'vue-router'
import { BASE_URL, api } from '@/helpers/api/http-commons.js'
import axios from 'axios'

export const useClientStore = defineStore('client', {
  state: () => {
    return {
      users: []
    }
  },
  getters: {},
  actions: {
    async assignUserAccount(data) {
      try {
        let config = {
          method: 'post',
          maxBodyLength: Infinity,
          url: BASE_URL + api.CLIENTS.ASSIGN_USER_ACCOUNT,
          headers: {
            'Content-Type': 'application/json'
          },
          data: data
        }
        const response = await axios.request(config)
        console.log(response.data)
        this.users = response.data.data
        return response.status
      } catch (error) {
        console.error('Error:', error)
        return error.response.status
      }
    },
    async revokeUserAccount(data) {
      try {
        let config = {
          method: 'delete',
          maxBodyLength: Infinity,
          url: BASE_URL + api.CLIENTS.REVOKE_USER_ACCOUNT,
          headers: {
            'Content-Type': 'application/json'
          },
          data: data
        }
        const response = await axios.request(config)
        console.log(response.data)
        this.users = response.data.data
        return response.status
      } catch (error) {
        console.error('Error:', error)
        return error.response.status
      }
    }
  }
})
