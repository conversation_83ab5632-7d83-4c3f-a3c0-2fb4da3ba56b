<template>
  <div>
    <p class="mb-5">Showing payments for {{ formattedStartDate }} to {{ formattedEndDate }}</p>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'

export default {
  setup() {
    // Function to get yesterday's midnight
    // function getYesterdaysMidnight() {
    //   const now = new Date()
    //   const yesterday = new Date(now)
    //   yesterday.setDate(now.getDate() - 1)
    //   yesterday.setHours(0, 0, 0, 0)
    //   return yesterday
    // }

    function getTodaysMidnight() {
      const now = new Date()
      now.setHours(0, 0, 0, 0)
      return now
    }

    const startDate = ref(getTodaysMidnight())
    const endDate = ref(new Date())

    const formattedStartDate = computed(() => {
      return formatDate(startDate.value)
    })

    const formattedEndDate = computed(() => {
      return formatTime(endDate.value)
    })

    function formatDate(date) {
      const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']
      const months = [
        'Jan',
        'Feb',
        'Mar',
        'Apr',
        'May',
        'Jun',
        'Jul',
        'Aug',
        'Sep',
        'Oct',
        'Nov',
        'Dec'
      ]

      const dayName = days[date.getDay()]
      const day = String(date.getDate()).padStart(2, '0')
      const monthName = months[date.getMonth()]
      const year = date.getFullYear()

      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')

      return `${dayName} ${day} ${monthName}, ${year} ${hours}:${minutes}:${seconds}`
    }

    function formatTime(date) {
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      return `${hours}:${minutes}`
    }

    // onMounted(() => {
    //   setInterval(() => {
    //     endDate.value = new Date()
    //   }, 60000) // Update every minute
    // })

    return {
      formattedStartDate,
      formattedEndDate
    }
  }
}
</script>

<style scoped>
/* Add any styles you need here */
</style>
