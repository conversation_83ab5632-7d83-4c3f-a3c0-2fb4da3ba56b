<template>
  <LayoutAuthenticated>
    <SectionMainFull>
      <div class="mb-6">
        <h1 class="text-3xl font-bold text-gray-900">Mobile Recons</h1>
      </div>
      <CardBoxModal
        v-model="isFilterModalActive"
        title="Find Recon"
        button="info"
        has-cancel
        button-label="Search"
        action-label="confirm"
        @confirm="filterDataFn()"
      >
        <div class="grid grid-cols-1 gap-2 lg:grid-cols-2">
          <FormField label="Start Date" help="">
            <FormControl
              v-model="filterData.init_start_date"
              type="datetime-local"
              name="start_date"
              placeholder="Start Date"
            />
          </FormField>
          <FormField label="End Date" help="">
            <FormControl
              v-model="filterData.init_end_date"
              type="datetime-local"
              name="start_date"
              placeholder="Start Date"
            />
          </FormField>
        </div>
        <div class="grid grid-cols-1 gap-2 lg:grid-cols-2">
          <FormField label="Status">
            <FormControl v-model="filterData.status" :options="statusOptions" />
          </FormField>
          <FormField label="Client">
            <multiselect
              v-model="selectedItems"
              :options="
                clientsData.map((client) => ({ id: client.id, name: client.business_name }))
              "
              :multiple="true"
              :showLabels="false"
              :close-on-select="false"
              placeholder="Select items"
              label="name"
              track-by="id"
              openDirection="above"
              :maxHeight="180"
            >
              <template #noResult> Oops! No clients found.</template>
            </multiselect>
          </FormField>
        </div>
      </CardBoxModal>
      <!-- UPDATE RECON STATUS MODEL -->
      <CardBoxModal
        v-model="isUpdateStatusModalActive"
        title="Update Recons Status"
        button="info"
        has-cancel
        button-label="Update"
        action-label="confirm"
        @confirm="updateReconsStatus()"
      >
        <div class="mb-4">
          <FormField label="Status">
            <FormControl v-model="filterData.status" :options="statusUpdateOptions" />
          </FormField>
        </div>
      </CardBoxModal>

      <div class="grid grid-cols-1 gap-6 lg:grid-cols-5 mb-6">
        <CardBoxWidget
          trend-type="down"
          color="text-blue-500"
          bgColor="bg-red-500"
          :icon="mdiCash"
          :number="summary.totalCollected"
          :count="summary.totalCount"
          prefix=""
          label="Total Collected"
          containClass="text-right border"
        />
        <CardBoxWidget
          trend-type="down"
          color="text-primeGreen"
          containClass="bg-primeGreen text-white text-right"
          :icon="mdiCash"
          :number="summary.totalMobileCommission"
          :count="summary.totalCount"
          prefix=""
          label="Mobile Commission"
        />
        <CardBoxWidget
          trend-type="down"
          color="text-primeGreen"
          containClass="bg-primeGreen text-white text-right"
          :icon="mdiCash"
          :number="summary.totalPrimeNetCommission"
          :count="summary.totalCount"
          prefix=""
          label="PrimeNet Commission"
        />
        <CardBoxWidget
          trend-type="down"
          color="text-primeGreen"
          containClass="bg-primeGreen text-white text-right"
          :icon="mdiCash"
          :number="summary.totalPartnerCommission"
          :count="summary.totalCount"
          prefix=""
          label="Partner Commission"
        />
        <CardBoxWidget
          trend-type="down"
          color="text-primeGreen"
          containClass="bg-primeGreen text-white text-right"
          :icon="mdiCash"
          :number="summary.totalMnoCommission"
          :count="summary.totalCount"
          prefix=""
          label="MNO Commission"
        />
      </div>
      <div
        class="flex justify-items-end items-center space-x-5 border-b py-2 border-gray-100 mb-3"
        style="overflow: auto"
      >
        <BaseButton
          color="contrast"
          :icon="mdiFilterOutline"
          outline
          label="Find Record"
          @click="startFilterModal(true)"
        />
        <BaseButton
          color="contrast"
          :icon="mdiFilterOutline"
          outline
          label="Update Status"
          @click="isUpdateStatusModalActive = true"
        />
        <BaseButton
          v-if="items.length"
          color="contrast"
          :icon="mdiExport"
          outline
          label="Export"
          @click="exportTransactions()"
        />
        <BaseIcon
          @click="reloadData()"
          :path="mdiReload"
          size="30"
          w=""
          h="h-10"
          class="text-gray-600 p-1 h-full border border-black rounded cursor-pointer hover:bg-black hover:text-white"
          title="Reload"
        />
      </div>
      <CardBox rounded="rounded-sm" class="mb-6 px-2" has-table style="overflow: auto">
        <DatabaseLoaderOne v-if="mainLoader" />
        <NoRecordSvgVue v-else-if="items.length == 0 && mainLoader == false" />

        <table v-else class="text-sm mt-1">
          <thead>
            <tr class="text-xs text-[#212529] leading-[1.5]">
              <th>MERCHANT</th>
              <th>ACCOUNT NUMBER</th>
              <th>ACCOUNT NAME</th>
              <th>CURRENCY</th>
              <th>TOTAL COLLECTED</th>
              <th>TOTAL REFUNDS</th>
              <th>MOBILE COMMISSION</th>
              <th>PRIMENET COMMISSION</th>
              <th>PARTNER COMMISSION</th>
              <th>MNO COMMISSION</th>
              <th>MERCHANT MOBILE SETTLEMENT</th>
              <th>SETTLEMENT STATUS</th>
              <th>DATE</th>
              <th>ACTION</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="data in items" :key="data.id">
              <td data-label="CLIENT">
                {{ data.client.business_name ?? '-' }}
              </td>
              <td data-label="ACCOUNT NUMBER">
                {{ data.account_number ?? '-' }}
              </td>
              <td data-label="ACCOUNT NAME">
                {{ data.account_name ?? '-' }}
              </td>
              <td data-label="CURRENCY">
                {{ data.currency ?? 'ZMW' }}
              </td>
              <td data-label="TOTAL COLLECTED">
                {{ data.total_collected ?? '-' }}
              </td>
              <td data-label="TOTAL REFUNDS">
                {{ data.total_refunds ?? '-' }}
              </td>
              <td data-label="MOBILE COMMISSION">
                {{ data.mobile_commission ?? '-' }}
              </td>
              <td data-label="PRIMENET COMMISSION">
                {{ data.primenet_commission ?? '-' }}
              </td>
              <td data-label="PARTNER COMMISSION">
                {{ data.partner_commission ?? '-' }}
              </td>
              <td data-label="MNO COMMISSION">
                {{ data.mno_commission ?? '-' }}
              </td>
              <td data-label="MERCHANT MOBILE SETTLEMENT">
                {{ data.merchant_momo_settlement ?? '-' }}
              </td>
              <td data-label="STATUS">
                {{ data.settlement_status }}
              </td>
              <td data-label="DATE">
                {{ new Date(data.created_at).toLocaleString() }}
              </td>
              <td class="before:hidden lg:w-1 whitespace-nowrap">
                <BaseButtons type="justify-start lg:justify-end" no-wrap>
                  <BaseButton
                    @click="viewRecon(data?.id)"
                    :icon="mdiEyeCircleOutline"
                    label="View"
                    color="contrast"
                    outline
                    small
                  />
                </BaseButtons>
              </td>
            </tr>
          </tbody>
        </table>
      </CardBox>
      <div
        class="p-3 lg:px-6 border-t border-gray-100 dark:border-slate-800"
        v-if="!mainLoader && items.length > 0 && reconStore?.recons?.total_pages > 1"
      >
        <Pagination
          v-model="currentPage"
          :total-items="summary.totalCount"
          :items-per-page="reconStore.limit"
        />
      </div>
      <Loading v-model="loading" />
    </SectionMainFull>
  </LayoutAuthenticated>
</template>
<script setup>
import {
  onMounted,
  onBeforeMount,
  computed,
  ref,
  toRefs,
  defineProps,
  watchEffect,
  watch
} from 'vue'
import { useSuccess } from '@/helpers/functions/notifications.js'
import axios from 'axios'
import { useRouter, useRoute } from 'vue-router'
import FormControl from '@/components/FormControl.vue'
import FormField from '@/components/FormField.vue'
import BreadcrumbBase from '@/components/Reusables/Breadcrumbs/BreadcrumbBase.vue'
import BreadcrumbContinue from '@/components/Reusables/Breadcrumbs/BreadcrumbContinue.vue'
import BreadcrumbEnd from '@/components/Reusables/Breadcrumbs/BreadcrumbEnd.vue'
import DatabaseLoaderOne from '@/components/Reusables/Loaders/DatabaseLoaderOne.vue'
import SpinnerOne from '@/components/Reusables/Loaders/SpinnerOne.vue'
import NoRecordSvgVue from '@/components/Reusables/Svgs/NoRecordSvg.vue'
import { useAuthStore } from '@/stores/auth.js'
import {
  mdiFileExcelBoxOutline,
  mdiEyeCircleOutline,
  mdiReload,
  mdiFilterOutline,
  mdiCash,
  mdiExport
} from '@mdi/js'
import { statusCodeToName, serviceCodeToName } from '@/helpers/functions/status.js'
import SectionMainFull from '@/components/SectionMainFull.vue'
import CardBox from '@/components/CardBox.vue'
import LayoutAuthenticated from '@/layouts/LayoutAuthenticated.vue'
import BaseButton from '@/components/BaseButton.vue'
import CardBoxModal from '@/components/CardBoxModal.vue'
import BaseIcon from '@/components/BaseIcon.vue'
import BaseButtons from '@/components/BaseButtons.vue'
import Multiselect from 'vue-multiselect'
import * as chartConfig from '@/components/Charts/chart.config.js'
import CardBoxWidget from '@/components/CardBoxWidget.vue'
import Pagination from '@/components/Pagination.vue'
import { endOfToday, format, startOfToday } from 'date-fns'
import Loading from '@/components/modals/Loading.vue'
import { useReconsStore } from '@/stores/recons.js'
const chartData = ref(null)

const statusUpdateOptions = ref([])

const selectedItems = ref([])

const authStore = useAuthStore()

const clientsData = computed(() => {
  return authStore.clientsData
})

const authUserData = computed(() => {
  return authStore.userData
})

onMounted(async () => {
  try {
    mainLoader.value = true
    await reconStore.getRecons(1, 'MOMO')

    if (authUserData.value.role == 'FINANCE') {
      statusUpdateOptions.value = [
        {
          label: 'Settled',
          id: 'SETTLED'
        }
      ]
    } else {
      statusUpdateOptions.value = [
        {
          label: 'Pending',
          id: 'PENDING'
        }
      ]
    }
  } catch (e) {
  } finally {
    mainLoader.value = false
  }
})

const items = computed(() => {
  return Array.isArray(reconStore.recons.data) ? reconStore.recons.data : []
})

const summary = computed(() => {
  return reconStore.recons.summary ?? {}
})

const isFilterModalActive = ref(false)
const isUpdateStatusModalActive = ref(false)
const isViewReconActive = ref(false)
const currentPage = ref(1)
const checkedRows = ref([])
const checkedDeleteRows = ref([])
const selectedViewData = ref([])
const searchValue = ref('')
const mainLoader = ref(false)
const reloaded = ref(false)
const loading = ref(false)
const reconStore = useReconsStore()
const router = useRouter()

const statusOptions = [
  { id: '', label: 'All Recons' },
  { id: 'SETTLED', label: 'Settled' },
  { id: 'PENDING', label: 'Pending' },
  { id: 'RECONCILED', label: 'Reconciled' }
]

const reconTypeOptions = [
  { id: '', label: 'All Recons' },
  { id: 'MOMO', label: 'Mobile Money' },
  { id: 'CARD', label: 'Card' }
]

const filterData = ref({
  init_start_date: format(startOfToday(), "yyyy-MM-dd'T'HH:mm"),
  init_end_date: format(endOfToday(), "yyyy-MM-dd'T'HH:mm"),
  start_date: '',
  start_time: '',
  end_date: '',
  end_time: '',
  status: statusOptions[0],
  recon_type: reconTypeOptions.find((option) => option.id === 'MOMO') || reconTypeOptions[1],
  phone_number: '',
  account_number: ''
})

const splitDateTimeLocal = async () => {
  const [start_date, start_time] = filterData.value.init_start_date.split('T')
  const [start_hours, start_minutes] = start_time.split(':')

  filterData.value.start_date = start_date
  filterData.value.start_time = `${start_hours}:${start_minutes}`
  const [end_date, end_time] = filterData.value.init_end_date.split('T')
  const [end_hours, end_minutes] = end_time.split(':')

  filterData.value.end_date = end_date
  filterData.value.end_time = `${end_hours}:${end_minutes}`
}

const startFilterModal = (status) => {
  isFilterModalActive.value = status
}

const viewRecon = (recondID) => {
  router.push({ name: 'recon-detail', params: { id: recondID } })
}

watch(currentPage, async () => {
  if (filterData.value.start_date) {
    await filterDataFn()
  } else {
    try {
      if (reloaded.value) {
        reloaded.value = false
        return
      }

      mainLoader.value = true
      await reconStore.getRecons(currentPage.value, 'MOMO')
    } catch (e) {
    } finally {
      mainLoader.value = false
    }
  }
})

const reloadData = async () => {
  try {
    mainLoader.value = true
    await reconStore.getRecons(1, 'MOMO')
    reloaded.value = true
    currentPage.value = 1
  } catch (e) {
  } finally {
    mainLoader.value = false
    clearstates()
  }
}

const filterDataFn = async () => {
  mainLoader.value = true
  await splitDateTimeLocal()

  try {
    const data = {
      from_date: filterData.value.start_date,
      from_date_time: filterData.value.start_time,
      to_date: filterData.value.end_date,
      to_date_time: filterData.value.end_time,
      ...(filterData.value.recon_type?.id && { recon_type: filterData.value.recon_type.id }),
      status: filterData.value.status.id,
      account_number: filterData.value.account_number,
      clients: selectedItems.value.map((selected) => selected.id),
      page: currentPage.value,
      limit: reconStore.limit
    }

    let res = await reconStore.getFilteredRecons(data)
  } catch (error) {
  } finally {
    mainLoader.value = false
  }
}

const updateReconsStatus = async () => {
  try {
    loading.value = true

    const data = {
      from_date: filterData.value.start_date,
      from_date_time: filterData.value.start_time,
      to_date: filterData.value.end_date,
      to_date_time: filterData.value.end_time,
      status: filterData.value.status.id,
      ...(filterData.value.recon_type?.id && { recon_type: filterData.value.recon_type.id }),
      account_number: filterData.value.account_number,
      clients: selectedItems.value.map((selected) => selected.id)
    }

    const response = await reconStore.updateReconsStatus(data)

    if (response.status != 200) {
      return
    }
  } catch (error) {
  } finally {
    loading.value = false
  }
}

const exportTransactions = async () => {
  try {
    loading.value = true

    const data = {
      from_date: filterData.value.start_date,
      from_date_time: filterData.value.start_time,
      to_date: filterData.value.end_date,
      to_date_time: filterData.value.end_time,
      status: filterData.value.status.id,
      ...(filterData.value.recon_type?.id && { recon_type: filterData.value.recon_type.id }),
      account_number: filterData.value.account_number,
      clients: selectedItems.value.map((selected) => selected.id)
    }

    const response = await reconStore.exportRecons(data)

    if (response.status != 200) {
      return
    }

    const url = window.URL.createObjectURL(new Blob([response.data]))
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', 'recons.xlsx')
    document.body.appendChild(link)
    link.click()
    link.remove()
  } catch (error) {
  } finally {
    loading.value = false
  }
}

const clearstates = () => {
  filterData.value = {
    init_start_date: format(startOfToday(), "yyyy-MM-dd'T'HH:mm"),
    init_end_date: format(endOfToday(), "yyyy-MM-dd'T'HH:mm"),
    start_date: '',
    start_time: '',
    end_date: '',
    end_time: '',
    business_name: '',
    status: statusOptions[0],
    recon_type: reconTypeOptions[0],
    service: serviceOptions[0],
    transaction_type: transactionTypeOptions[0],
    phone_number: '',
    account_number: '',
    transaction_id: '',
    external_id: '',
    narration: ''
  }
}
</script>

<style src="vue-multiselect/dist/vue-multiselect.css"></style>
<style>
/* Change selected option background to blue */
.multiselect__option--selected {
  background: #3490dc !important;
}

/* Change highlighted option background (when hovering/navigating) */
.multiselect__option--highlight {
  background: #2779bd !important;
  color: white !important;
}

/* Change tag background color */
.multiselect__tag {
  background: #3490dc !important;
}

/* Change tag icon color */
.multiselect__tag-icon:after {
  color: white !important;
}
.multiselect__tag-icon:hover {
  background: #2779bd !important;
}
</style>
