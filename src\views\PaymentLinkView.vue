<template>
  <div class="h-screen flex justify-center items-center">
    <half-circle-spinner v-if="loading" :animation-duration="1000" :size="60" color="#EC3490" />
    <PaymentLinkNotFound v-if="!loading" />
  </div>
</template>
<script setup>
import { usePaymentLinksStore } from '@/stores/paymentLinks'
import sleep from 'sleep-promise'
import { HalfCircleSpinner } from 'epic-spinners'
import { onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import PaymentLinkNotFound from '@/components/PaymentLinkNotFound.vue'

const route = useRoute()
const loading = ref(true)
const error = ref('')
const paymentLinksStore = usePaymentLinksStore()

onMounted(async () => {
  try {
    var id = route.params.id

    var resp = await paymentLinksStore.getPaymentLink(id)

    if (resp == 200) {
      window.location.href = paymentLinksStore.link
    } else if (resp == 404) {
      loading.value = false
      error.value = 'Link expired or does not exist'
    } else {
      loading.value = false
      error.value = 'Something went wrong, please try again later'
    }
  } catch (err) {
    loading.value = false
  }
})
</script>
