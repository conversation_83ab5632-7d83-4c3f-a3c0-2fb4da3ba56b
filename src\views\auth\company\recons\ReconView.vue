<template>
  <LayoutAuthenticated title="Recon Detail">
    <SectionMainFull>
      <DatabaseLoaderOne v-if="loading" />
      <div
        class="max-w-3xl mx-auto p-8 bg-white shadow-[0_2px_10px_-3px_rgba(6,81,237,0.3)]"
        v-else
      >
        <CardBox
          rounded="rounded-sm"
          class="mb-6 px-2"
          has-table
          style="overflow: auto"
          v-if="recon"
        >
          <button
            class="bg-[#090133] px-3 py-1 rounded text-gray-200"
            @click="isUpdateReconSettlementStatusModalActive = true"
            v-if="
              authUserData.role == 'RECON_COORDINATOR' ||
              authUserData.role == 'FINANCE' ||
              authUserData.role == 'SUPERADMIN'
            "
          >
            Update Settlement Status
          </button>
          <table class="text-sm mt-1">
            <tbody class="text-left">
              <tr class="flex">
                <td class="w-[15rem] font-[500] text-[#333] text-sm">Client</td>
                <td class="flex-grow">{{ recon.client.business_name ?? '-' }}</td>
              </tr>
              <tr class="flex">
                <td class="w-[15rem] font-[500] text-[#333] text-sm">ACCOUNT NUMBER</td>
                <td class="flex-grow">{{ recon.account_number ?? '-' }}</td>
              </tr>
              <tr class="flex">
                <td class="w-[15rem] font-[500] text-[#333] text-sm">ACCOUNT NAME</td>
                <td class="flex-grow">{{ recon.account_name ?? '-' }}</td>
              </tr>
              <tr class="flex">
                <td class="w-[15rem] font-[500] text-[#333] text-sm">TOTAL COLLECTED</td>
                <td class="flex-grow">{{ recon.total_collected ?? '-' }}</td>
              </tr>
              <tr class="flex">
                <td class="w-[15rem] font-[500] text-[#333] text-sm">TOTAL REFUNDS</td>
                <td class="flex-grow">{{ recon.total_refunds ?? '-' }}</td>
              </tr>
              <tr class="flex">
                <td class="w-[15rem] font-[500] text-[#333] text-sm">MOBILE COMMISSION</td>
                <td class="flex-grow">{{ recon.mobile_commission ?? '-' }}</td>
              </tr>
              <tr class="flex">
                <td class="w-[15rem] font-[500] text-[#333] text-sm">CARD COMMISSION</td>
                <td class="flex-grow">{{ recon.card_commission ?? '-' }}</td>
              </tr>
              <tr class="flex">
                <td class="w-[15rem] font-[500] text-[#333] text-sm">PRIMENET COMMISSION</td>
                <td class="flex-grow">{{ recon.primenet_commission ?? '-' }}</td>
              </tr>
              <tr class="flex">
                <td class="w-[15rem] font-[500] text-[#333] text-sm">PARTNER COMMISSION</td>
                <td class="flex-grow">{{ recon.partner_commission ?? '-' }}</td>
              </tr>
              <tr class="flex">
                <td class="w-[15rem] font-[500] text-[#333] text-sm">MNO COMMISSION</td>
                <td class="flex-grow">{{ recon.mno_commission ?? '-' }}</td>
              </tr>
              <tr class="flex">
                <td class="w-[15rem] font-[500] text-[#333] text-sm">
                  MERCHANT MOBILE SETTLEMENET
                </td>
                <td class="flex-grow">{{ recon.merchant_momo_settlement ?? '-' }}</td>
              </tr>
              <tr class="flex">
                <td class="w-[15rem] font-[500] text-[#333] text-sm">
                  MERCHANT MOBILE SETTLEMENET
                </td>
                <td class="flex-grow">{{ recon.merchant_card_settlement ?? '-' }}</td>
              </tr>
              <tr class="flex">
                <td class="w-[15rem] font-[500] text-[#333] text-sm">SETTLEMENT STATUS</td>
                <td class="flex-grow">{{ recon.settlement_status ?? '-' }}</td>
              </tr>
              <tr class="flex">
                <td class="w-[15rem] font-[500] text-[#333] text-sm">MOBILE COMMISSION RATES</td>
                <td class="flex-grow">{{ recon.momo_comm_rates + '%' ?? '-' }}</td>
              </tr>
              <tr class="flex">
                <td class="w-[15rem] font-[500] text-[#333] text-sm">CARD COMMISSION RATES</td>
                <td class="flex-grow">{{ recon.card_comm_rates ?? '-' }}</td>
              </tr>
              <tr class="flex">
                <td class="w-[15rem] font-[500] text-[#333] text-sm">BANK COMM RATES</td>
                <td class="flex-grow">{{ recon.bank_commission ?? '-' }}</td>
              </tr>
              <tr class="flex">
                <td class="w-[15rem] font-[500] text-[#333] text-sm">REVENUE SHARE</td>
                <td class="flex-grow">{{ recon.surcharge ?? '-' }}</td>
              </tr>
              <tr class="flex">
                <td class="w-[15rem] font-[500] text-[#333] text-sm">
                  PRIMENET REVENUE SHARE RATES
                </td>
                <td class="flex-grow">{{ recon.primenet_revenue_share ?? '-' }}</td>
              </tr>
              <tr class="flex">
                <td class="w-[15rem] font-[500] text-[#333] text-sm">SETTLEMENT MODEL</td>
                <td class="flex-grow">{{ recon.model ?? '-' }}</td>
              </tr>
              <tr class="flex">
                <td class="w-[15rem] font-[500] text-[#333] text-sm">RECON DATE</td>
                <td class="flex-grow">
                  {{ dayjs(recon.created_at).format('YYYY-MM-DD HH:mm:ss') }}
                </td>
              </tr>
              <tr class="flex">
                <td class="w-[15rem] font-[500] text-[#333] text-sm">DATE MODIFIED</td>
                <td class="flex-grow">
                  {{ dayjs(recon.updated_at).format('YYYY-MM-DD HH:mm:ss') }}
                </td>
              </tr>
            </tbody>
          </table>
        </CardBox>
      </div>

      <!-- MODALS -->

      <UpdateReconSettlementStatus
        v-model="isUpdateReconSettlementStatusModalActive"
        :recon="recon"
        v-if="recon"
      />
    </SectionMainFull>
  </LayoutAuthenticated>
</template>
<script setup>
import DatabaseLoaderOne from '@/components/Reusables/Loaders/DatabaseLoaderOne.vue'
import SectionMainFull from '@/components/SectionMainFull.vue'
import CardBox from '@/components/CardBox.vue'
import LayoutAuthenticated from '@/layouts/LayoutAuthenticated.vue'
import { computed, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import dayjs from 'dayjs'
import { useReconsStore } from '@/stores/recons'
import UpdateReconSettlementStatus from '@/components/modals/UpdateReconSettlementStatus.vue'
import { useAuthStore } from '@/stores/auth'

const isUpdateReconSettlementStatusModalActive = ref(false)
const isRefundModalActive = ref(false)

var reconStore = useReconsStore()
const authStore = useAuthStore()

const route = useRoute()
const router = useRouter()
var loading = ref(true)

const recon = computed(() => {
  return reconStore.recon
})

const authUserData = computed(() => {
  return authStore.userData
})

const startRefundModal = (status) => {
  isRefundModalActive.value = status
}

const refundModalEmit = (event, item) => {
  startRefundModal(false)
}

onMounted(async () => {
  await reconStore.getRecon(route.params.id)
  loading.value = false
})
</script>
