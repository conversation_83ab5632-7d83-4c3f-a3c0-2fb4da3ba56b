import { STATUS } from '@/helpers/params/status.js'
import { SERVICE } from '@/helpers/params/service'
import { STATUS_CODE_MESSAGE } from '@/helpers/params/statusMessages'

const statusCodeToName = (code) => {
  const entry = STATUS.find((item) => item['Code'] == code)
  return entry ? entry['Name'] : 'Unknown'
}

const serviceCodeToName = (code) => {
  const entry = SERVICE.find((item) => item['Code'] == code)
  return entry ? entry['Name'] : 'Unknown'
}

const alertCodeToTitle = (code) => {
  const entry = STATUS_CODE_MESSAGE.find((item) => item['Code'] == code)
  return entry ? entry['Title'] : 'Unknown'
}

const alertCodeToMessage = (code) => {
  const entry = STATUS_CODE_MESSAGE.find((item) => item['Code'] == code)
  return entry ? entry['Message'] : 'Unknown'
}

const paymentLinkStatusColors = (status) => {
  if (status == 'ACTIVE') {
    return 'bg-green-100 text-green-500'
  } else if (status == 'EXPIRED') {
    return 'bg-red-100 text-red-500'
  } else {
    return 'bg-gray-100 text-gray-500'
  }
}

export {
  statusCodeToName,
  serviceCodeToName,
  alertCodeToTitle,
  alertCodeToMessage,
  paymentLinkStatusColors
}
