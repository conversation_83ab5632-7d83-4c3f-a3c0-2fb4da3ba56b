<template>
  <LayoutAuthenticated>
    <SectionMain>
      <SectionTitleLineWithButton :icon="mdiAccount" :title="`Users / ${displayName}`" small>
        <BaseButton
          :icon="mdiArrowLeft"
          label="Back"
          color="contrast"
          @click="goBack()"
          rounded-full
          small
        />
      </SectionTitleLineWithButton>
      <CardBoxModal
        v-model="isAssignAccountModalActive"
        :title="`Assign ${selectedViewData.name}'s account`"
        button="info"
        button-label=""
        action-label="confirm"
      >
        <div class="grid grid-cols-1 gap-2 lg:grid-cols-1">
          <FormField label="Select Account">
            <FormControl v-model="formData.accountId" :options="statusOptions" />
          </FormField>
        </div>
        <div class="flex space-x-2">
          <BaseButton
            v-if="formData.accountId"
            label="Assign"
            color="success"
            @click="assignUserAcountFn()"
            :disabled="loading"
            :loading="loading"
          />
          <BaseButton v-else label="Assign" color="success" disabled />
          <BaseButton
            label="Close"
            color="info"
            outline
            :disabled="loading"
            @click="clearstates()"
          />
        </div>
      </CardBoxModal>
      <CardBoxModal
        v-model="isRevokeAccountModalActive"
        title="Revoke Account"
        button="info"
        button-label=""
        action-label="confirm"
      >
        <div class="text-lg my-2">
          Are you sure you want to revoke
          <span class="font-bold">{{ selectedAccountData.business_name }}</span
          >?
        </div>
        <div class="flex space-x-2">
          <BaseButton
            label="Revoke"
            color="contrast"
            :disabled="loading"
            :loading="loading"
            @click="revokeUserAcountFn()"
          />
          <BaseButton
            label="Close"
            color="info"
            outline
            :disabled="loading"
            @click="clearstates()"
          />
        </div>
      </CardBoxModal>
      <CardBoxModal
        v-model="isUpdateUserModalActive"
        title="Update"
        button="info"
        button-label=""
        action-label="confirm"
      >
        <FormField label="Fullname" help="">
          <FormControl v-model="formData.fullname" type="text" name="fullname" placeholder="" />
        </FormField>
        <FormField label="Email" help="">
          <FormControl v-model="formData.email" type="email" name="email" placeholder="" />
        </FormField>
        <FormField label="Phone" help="">
          <FormControl v-model="formData.phone" type="tel" name="tel" placeholder="" />
        </FormField>
        <div class="flex space-x-2">
          <BaseButton
            label="Update"
            color="info"
            @click="updateUserFn()"
            :disabled="loading"
            :loading="loading"
          />
          <BaseButton
            label="Close"
            color="info"
            outline
            :disabled="loading"
            @click="clearstates()"
          />
        </div>
      </CardBoxModal>
      <CardBoxModal
        v-model="isDeleteUserModalActive"
        title="Delete"
        button="info"
        button-label=""
        action-label="confirm"
      >
        <div class="text-lg my-2">
          Are you sure you want to delete <span class="font-bold">{{ selectedViewData.name }}</span
          >?
        </div>
        <div class="flex space-x-2">
          <BaseButton
            label="Delete"
            color="danger"
            @click="deleteUserFn()"
            :disabled="loading"
            :loading="loading"
          />
          <BaseButton
            label="Cancel"
            color="info"
            outline
            :disabled="loading"
            @click="clearstates()"
          />
        </div>
      </CardBoxModal>

      <CardBox padding="0" v-if="userData">
        <div class="grid grid-cols-1 mt-5 lg:grid-cols-2">
          <BaseLevel type="justify-around lg:justify-center">
            <div class="space-y-3 text-center md:text-left lg:mx-12">
              <div
                class="rounded-full flex justify-center mx-auto items-center text-center bg-gray-100 dark:bg-slate-800 h-40 w-40 text-6xl text-gray-500 font-thin"
              >
                {{ initials }}
              </div>
              <h1 class="text-2xl">
                <div class="text-2xl text-center">
                  <span>{{ userData?.name ?? '' }}</span>
                </div>
              </h1>
              <!-- <p>Last login <b>12 mins ago</b> from <b>127.0.0.1</b></p> -->
              <div class="flex justify-center md:block">
                <!-- <PillTag label="Verified" color="info" :icon="mdiCheckDecagram" /> -->
              </div>
            </div>
          </BaseLevel>
          <div>
            <div class="flex justify-end">
              <BaseButtons
                v-if="userData?.role != 'SUPERADMIN' && userData.role != authUserData.role"
              >
                <BaseButton
                  @click="startUpdateUser(true)"
                  :icon="mdiPen"
                  label=""
                  color="contrast"
                  outline
                  small
                />
                <BaseButton
                  @click="startDeleteUser(true)"
                  :icon="mdiTrashCan"
                  label=""
                  color="danger"
                  outline
                  small
                />
              </BaseButtons>
            </div>
            <div>
              <div class="flex space-x-3 mb-5">
                <BaseIcon
                  :path="mdiCellphone"
                  size="20"
                  w=""
                  h=""
                  class="text-gray-600 border-gray-600"
                />
                <div>{{ userData?.phone_number ?? 'N/A' }}</div>
              </div>
              <div class="flex space-x-3 mb-5">
                <BaseIcon
                  :path="mdiEmailOutline"
                  size="20"
                  w=""
                  h=""
                  class="text-gray-600 border-gray-600"
                />
                <div>{{ userData?.email ?? 'N/A' }}</div>
              </div>
              <div class="flex space-x-3 mb-5">
                <BaseIcon
                  :path="mdiBriefcaseOutline"
                  size="20"
                  w=""
                  h=""
                  class="text-gray-600 border-gray-600"
                />
                <div>{{ userData?.role ?? 'N/A' }}</div>
              </div>
              <div class="flex space-x-3 mb-5">
                <BaseIcon
                  :path="mdiCalendarRange"
                  size="20"
                  w=""
                  h=""
                  class="text-gray-600 border-gray-600"
                />
                <div>{{ new Date(userData?.created_at).toLocaleString() }}</div>
              </div>
            </div>
          </div>
        </div>
      </CardBox>
      <div
        class="grid grid-cols-1 mt-5 lg:grid-cols-1"
        v-if="userData?.role != 'ADMIN' && userData?.role != 'SUPERADMIN'"
      >
        <CardBox rounded="rounded-sm" class="mb-6 px-2" has-table style="overflow: auto">
          <SectionTitleLineWithButton :icon="mdiDomain" title="Accounts" small>
            <BaseButton
              @click="startAssignAccount(true)"
              :icon="mdiPlus"
              label="Assign"
              color="contrast"
              outline
              small
            />
          </SectionTitleLineWithButton>
          <DatabaseLoaderOne v-if="items.length == 0 && mainLoader" />
          <NoRecordSvgVue v-else-if="items.length == 0 && mainLoader == false" />
          <table v-else>
            <thead>
              <tr>
                <th>NAME</th>
                <th>PHONE</th>
                <th>EMAIL</th>
                <th>ADDRESS</th>
                <th>ACTION</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="data in itemsPaginated" :key="data.id">
                <td data-label="NAME">
                  {{ data.business_name ?? '-' }}
                </td>
                <td data-label="PHONE">
                  {{ data.phone_number ?? '-' }}
                </td>
                <td data-label="EMAIL">
                  {{ data.email ?? '-' }}
                </td>
                <td data-label="ADDRESS">
                  {{ data.address ?? '-' }}
                </td>
                <td class="before:hidden lg:w-1 whitespace-nowrap">
                  <BaseButtons type="justify-start lg:justify-end" no-wrap>
                    <BaseButton
                      @click="startRevokeAccount(true, data)"
                      :icon="mdiClose"
                      label="Revoke"
                      color="contrast"
                      outline
                      small
                    />
                  </BaseButtons>
                </td>
              </tr>
            </tbody>
          </table>

          <div class="p-3 lg:px-6 border-t border-gray-100 dark:border-slate-800">
            <BaseLevel>
              <BaseButtons>
                <!-- Start button -->
                <BaseButton
                  :label="'Start'"
                  :color="currentPage === 0 ? 'lightDark' : 'whiteDark'"
                  small
                  @click="goToPage(0)"
                />

                <!-- Previous button -->
                <BaseButton
                  :label="'Previous'"
                  :color="currentPage === 0 ? 'lightDark' : 'whiteDark'"
                  small
                  @click="previousPage"
                />

                <!-- Next button -->
                <BaseButton
                  :label="'Next'"
                  :color="currentPage === numPages - 1 ? 'lightDark' : 'whiteDark'"
                  small
                  @click="nextPage"
                />

                <!-- End button -->
                <BaseButton
                  :label="'End'"
                  :color="currentPage === numPages - 1 ? 'lightDark' : 'whiteDark'"
                  small
                  @click="goToPage(numPages - 1)"
                />
              </BaseButtons>
              <small>Page {{ currentPageHuman }} of {{ numPages }}</small>
            </BaseLevel>
          </div>
        </CardBox>
      </div>
    </SectionMain>
  </LayoutAuthenticated>
</template>
<script setup>
import BaseButton from '@/components/BaseButton.vue'
import BaseButtons from '@/components/BaseButtons.vue'
import BaseIcon from '@/components/BaseIcon.vue'
import BaseLevel from '@/components/BaseLevel.vue'
import CardBox from '@/components/CardBox.vue'
import CardBoxModal from '@/components/CardBoxModal.vue'
import FormControl from '@/components/FormControl.vue'
import FormField from '@/components/FormField.vue'
import DatabaseLoaderOne from '@/components/Reusables/Loaders/DatabaseLoaderOne.vue'
import NoRecordSvgVue from '@/components/Reusables/Svgs/NoRecordSvg.vue'
import SectionMain from '@/components/SectionMain.vue'
import SectionTitleLineWithButton from '@/components/SectionTitleLineWithButton.vue'
import { useInfo, useSuccess, useWarning } from '@/helpers/functions/notifications.js'
import LayoutAuthenticated from '@/layouts/LayoutAuthenticated.vue'
import { useAuthStore } from '@/stores/auth.js'
import { useClientStore } from '@/stores/client.js'
import { useUserStore } from '@/stores/user.js'
import {
  mdiAccount,
  mdiArrowLeft,
  mdiBriefcaseOutline,
  mdiCellphone,
  mdiClose,
  mdiDomain,
  mdiEmailOutline,
  mdiPen,
  mdiPlus,
  mdiTrashCan,
  mdiCalendarRange
} from '@mdi/js'
import { computed, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
const route = useRoute()
const router = useRouter()
const clientStore = useClientStore()
const authStore = useAuthStore()
const userStore = useUserStore()
const infoPush = useInfo()
const successPush = useSuccess()
const warningPush = useWarning()
const loading = ref(false)
const mainLoader = ref(false)
const perPage = ref(5)
const currentPage = ref(0)
const isDeleteUserModalActive = ref(false)
const isAssignAccountModalActive = ref(false)
const isRevokeAccountModalActive = ref(false)
const isUpdateUserModalActive = ref(false)
const selectedViewData = ref([])
const selectedAccountData = ref([])
const formData = ref({
  accountId: '',
  fullname: '',
  email: '',
  phone: ''
})
const statusOptions = authStore.clientsData.map((item) => ({
  id: item.id,
  label: item.business_name
}))
const userData = computed(() => {
  return userStore.user.user
})
const clientData = computed(() => {
  return userStore.user.clients
})
const companyData = computed(() => {
  return userStore.user.company
})
const displayName = computed(() => {
  return userData.value?.name
})
const initials = computed(() => {
  if (userData.value && userData.value.name) {
    return `${userData.value.name.substring(0, 1)}`
  } else {
    return 'N/A'
  }
})

const authUserData = computed(() => {
  return authStore.userData
})

const startAssignAccount = (state) => {
  selectedViewData.value = userData.value
  isAssignAccountModalActive.value = state
}
const startRevokeAccount = (state, data) => {
  selectedAccountData.value = data
  selectedViewData.value = userData.value
  isRevokeAccountModalActive.value = state
}
const startUpdateUser = (state) => {
  selectedViewData.value = userData.value
  formData.value.fullname = userData.value.name
  formData.value.email = userData.value.email
  formData.value.phone = userData.value.phone_number
  isUpdateUserModalActive.value = state
}
const startDeleteUser = (state) => {
  selectedViewData.value = userData.value
  isDeleteUserModalActive.value = state
}

const assignUserAcountFn = async () => {
  loading.value = true
  try {
    const data = JSON.stringify({
      client_id: formData.value.accountId.id.toString(),
      user_id: selectedViewData.value.id.toString()
    })
    let res = await clientStore.assignUserAccount(data)
    if (res == 201) {
      successPush(
        'Successfully Assigned!',
        `${selectedViewData.value.name} has been assigned to ${formData.value.accountId.label}`
      )
      await userStore.getUserDetails(route.params.id)
    } else if (res == 409) {
      warningPush('Failed to Assign', `${selectedViewData.value.name} already has an account`)
    } else if (res == 404) {
      warningPush('Something is missing', 'Kindly check your input')
    } else {
      warningPush('Failed to Process', 'Please try again.')
    }
  } catch (error) {
    warningPush('Something went wrong', 'Please try again.')
  } finally {
    clearstates()
  }
}
const revokeUserAcountFn = async () => {
  loading.value = true
  try {
    const data = JSON.stringify({
      client_id: selectedAccountData.value.id.toString(),
      user_id: userData.value.id.toString()
    })
    let res = await clientStore.revokeUserAccount(data)
    if (res == 200) {
      successPush(
        'Successfully Revoked!',
        `${selectedViewData.value.name} has been removed from ${selectedAccountData.value.business_name}`
      )
      await userStore.getUserDetails(route.params.id)
    } else if (res == 404) {
      warningPush('Something is missing', 'Kindly check your input')
    } else if (res == 409) {
      warningPush('Failed to revoke', `${selectedViewData.value.name} has already been revoked`)
    } else {
      warningPush('Failed to Process', 'Please try again.')
    }
  } catch (error) {
    warningPush('Something went wrong', 'Please try again.')
  } finally {
    clearstates()
  }
}
const updateUserFn = async () => {
  loading.value = true
  try {
    const data = JSON.stringify({
      name: formData.value.fullname,
      email: formData.value.email,
      phone_number: formData.value.phone
    })
    let res = await userStore.updateUser(data, selectedViewData.value.id)
    if (res == 200) {
      successPush('Successfully Updated', '')
      await userStore.getUserDetails(route.params.id)
    } else if (res == 400) {
      warningPush('Failed to process', 'Fields should not be left empty!')
    } else if (res == 409) {
      warningPush('Duplicate Details', 'Email or Phone already taken')
    } else {
      warningPush('Failed to Proceed', 'Check your inputs')
    }
  } catch (error) {
    warningPush('Something went wrong', 'Please try again.')
  } finally {
    clearstates()
  }
}
const deleteUserFn = async () => {
  loading.value = true
  try {
    let res = await userStore.deleteUser(selectedViewData.value.id)
    if (res == 200) {
      successPush('Successfully Deleted', '')
      router.push({ name: 'users' })
    } else {
      warningPush('Failed to Proceed', 'Please try again.')
    }
  } catch (error) {
    warningPush('Something went wrong', 'Please try again.')
  } finally {
    clearstates()
  }
}
const clearstates = () => {
  loading.value = false
  formData.value = []
  selectedViewData.value = []
  selectedAccountData.value = []
  isDeleteUserModalActive.value = false
  isAssignAccountModalActive.value = false
  isUpdateUserModalActive.value = false
  isRevokeAccountModalActive.value = false
}
const goBack = () => {
  router.go(-1)
}
const items = computed(() => {
  return Array.isArray(userStore.user.clients) ? userStore.user.clients : []
})
const itemsPaginated = computed(() =>
  items.value.slice(perPage.value * currentPage.value, perPage.value * (currentPage.value + 1))
)
const numPages = computed(() => Math.ceil(items.value.length / perPage.value))
const currentPageHuman = computed(() => currentPage.value + 1)
const maxPaginationButtons = 2
const visiblePages = computed(() => {
  const pagesList = []
  const start = Math.max(0, currentPage.value - maxPaginationButtons)
  const end = Math.min(numPages.value - 1, currentPage.value + maxPaginationButtons)
  return items.value.slice(start, end + 1)
})

const goToPage = (page) => {
  currentPage.value = page
}
const goToNextPage = (page) => {
  currentPage.value = page
}

const previousPage = () => {
  if (currentPage.value > 0) {
    currentPage.value -= 1
  }
}

const nextPage = () => {
  if (currentPage.value < numPages.value - 1) {
    currentPage.value += 1
  }
}
onMounted(async () => {
  try {
    mainLoader.value = true
    await userStore.getUserDetails(route.params.id)
  } catch (e) {
  } finally {
    mainLoader.value = false
  }
})
</script>
<style scoped></style>
