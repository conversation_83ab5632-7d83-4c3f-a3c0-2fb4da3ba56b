export const chartColors = {
  default: {
    primary: "#00D1B2",
    info: "#209CEE",
    danger: "#FF3860",
    warning: "#ffa500"
  },
};

const randomChartData = (n) => {
  const data = n;

  // for (let i = 0; i < n; i++) {
  //   data.push(Math.round(Math.random() * 200));
  // }

  return data;
};

const datasetObject = (color, points, title) => {
  return {
    label: title,
    fill: false,
    borderColor: chartColors.default[color],
    backgroundColor: chartColors.default[color],
    borderDash: [],
    borderDashOffset: 0.0,
    // borderWidth: 2,
    borderRadius: 5,
    borderSkipped: false,
    // pointBackgroundColor: chartColors.default[color],
    // pointBorderColor: "rgba(255,255,255,0)",
    // pointHoverBackgroundColor: chartColors.default[color],
    // pointBorderWidth: 20,
    // pointHoverRadius: 4,
    // pointHoverBorderWidth: 15,
    // pointRadius: 4,
    data: randomChartData(points),
    tension: 0.5,
    cubicInterpolationMode: "default",
  };
};

export const sampleChartData = (tot,suc,fai) => {
  const labels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug','Sep','Oct','Nov','Dec'];
  const total = tot;
  const success = suc;
  const failed = fai;
  // for (let i = 1; i <= points; i++) {
  //   labels.push(`${i}`);
  // }

  return {

    labels,
    datasets: [
      datasetObject("info", total, "Total"),
      datasetObject("primary", success, "Success"),
      datasetObject("danger", failed, "Failed")
    ],
  };
};
