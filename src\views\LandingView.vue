<template>
<body>
<!-- Opened Nav in Mobile, you can use javascript/jQuery -->
<div id="nav-opened" class="fixed left-0 right-0 hidden bg-white mx-2 mt-16 rounded-br rounded-bl shadow z-10">
  <div class="p-2 divide-y divide-gray-600 flex flex-col">
    <a href="#about" class="p-2 font-semibold hover:text-indigo-700">About</a>
    <a href="#whyus" class="p-2 font-semibold hover:text-indigo-700">Why Us ?</a>
    <a href="#showcase" class="p-2 font-semibold hover:text-indigo-700">Our Products</a>
  </div>
</div>

<header id="up" class="bg-center bg-fixed bg-no-repeat bg-center bg-cover h-screen relative">
  <!-- Overlay Background + Center Control -->
  <div class="h-screen bg-opacity-50 bg-black flex items-center justify-center" style="background: rgb(4 3 3 / 79%)">
    <div class="mx-2 text-center">
      <h1 class="text-gray-100 font-extrabold text-4xl xs:text-5xl md:text-6xl">
        <span class="text-white">PRIMENET</span>
         </h1>
         <h2 class="text-gray-200 font-extrabold text-3xl xs:text-4xl md:text-5xl leading-tight">
          <!-- COMING SOON -->
         </h2>
         <div class="inline-flex">
         <router-link to="/login" class="p-2 my-5 mx-2 bg-indigo-700 hover:bg-indigo-800 font-extralight text-white rounded border-2 border-transparent hover:border-indigo-800 shadow-md transition duration-500 md:text-xl">EXPERIENCE</router-link>
         </div>
      </div>
  </div>
</header>
</body>
</template>
<script setup>
</script>
<style scoped>
/*
You may need this for responsive background
header {
background: url('bg-425.jpg');
}

@media only screen and (min-width:640px) {
header {
  background: url('bg-640.jpg');
}
}

@media only screen and (min-width:768px) {
header {
  background: url('bg-768.jpg');
}
}

@media only screen and (min-width:1024px) {
header {
  background: url('bg-1024.jpg');
}
}

@media only screen and (min-width:1025px) {
header {
  background: url('bg-max.jpg');
}
} */
/* Default background by https://www.pexels.com/@knownasovan */
header {
  background:url('/src/assets/images/backgrounds/bg-1.jpg');
    height: 100%;
background-position: center;
background-repeat: no-repeat;
background-size: cover;
}
</style>
