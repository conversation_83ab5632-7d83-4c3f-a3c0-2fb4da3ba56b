<template>
 <RouterView />
</template>
<script setup>
import { onMounted, computed, ref, defineProps, reactive } from "vue";
import { RouterView } from 'vue-router'
import { useAuthStore } from "@/stores/auth.js";
import { logout } from "@/helpers/functions/auth.js"
const auth = useAuthStore();
const companyData = computed(() => {
  return auth.companyData;
})
onMounted(async () => {
  localStorage.removeItem('account');
  if (companyData == "" || companyData == null) {
    logout()
  }
});
</script>
