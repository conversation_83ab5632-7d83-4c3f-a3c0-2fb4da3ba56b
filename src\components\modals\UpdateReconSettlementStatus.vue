<template>
  <CardBoxModal
    v-model="model"
    title="Update Settlement Status"
    button="info"
    button-label=""
    action-label="confirm"
  >
    <div class="grid grid-cols-1 lg:grid-cols-1">
      <FormField label="Status">
        <div>
          <FormControl
            v-model="form.status"
            type="select"
            name="status"
            :options="transactionStatuses"
          />
        </div>
      </FormField>
    </div>
    <div class="flex mt-3">
      <BaseButton label="Update" color="info" outline @click="updateSettlementStatus" />
    </div>
  </CardBoxModal>
  <Loading v-model="isLoading" />
</template>

<script setup>
import { onMounted, reactive, ref } from 'vue'
import BaseButton from '../BaseButton.vue'
import CardBoxModal from '../CardBoxModal.vue'
import FormField from '../FormField.vue'
import FormControl from '../FormControl.vue'
import { usePush } from 'notivue'
import Loading from './Loading.vue'
import { useReconsStore } from '@/stores/recons'
import { useAuthStore } from '@/stores/auth'
import { computed } from 'vue'

const props = defineProps(['recon'])

const form = reactive({
  status: {}
})

const model = defineModel()
const isLoading = ref(false)
const push = usePush()
const reconStore = useReconsStore()
const authStore = useAuthStore()

const authUserData = computed(() => {
  return authStore.userData
})

async function updateSettlementStatus() {
  if (!form.status) {
    push.error({ message: 'Kindly fill out all the fields', duration: 1000 })
    return
  }

  isLoading.value = true
  // await sleep(3000)
  const res = await reconStore.updateSettlementStatus({
    status: form.status.id,
    id: props.recon.id
  })

  isLoading.value = false

  if (res.status != 200) {
    if (res.status == 404) {
      push.error({ message: 'Recon not found', duration: 1000 })
    } else {
      push.error({ message: 'Something went wrong', duration: 1000 })
    }
  } else {
    push.success({ message: 'Settlement Status Updated', duration: 1000 })
  }
}

const transactionStatuses = ref([])

onMounted(() => {
  form.status = {
    label: props.recon.settlement_status,
    id: props.recon.settlement_status
  }

  if (authUserData.value.role == 'FINANCE') {
    transactionStatuses.value = [
      {
        label: 'Settled',
        id: 'SETTLED'
      }
    ]
  } else {
    transactionStatuses.value = [
      {
        label: 'Pending',
        id: 'PENDING'
      }
    ]
  }
})
</script>

<style lang="scss" scoped></style>
