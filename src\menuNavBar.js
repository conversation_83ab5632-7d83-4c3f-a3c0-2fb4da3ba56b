import {
  mdiMenu,
  mdiClockOutline,
  mdiCloud,
  mdiCrop,
  mdiAccount,
  mdiLock,
  mdiCogOutline,
  mdiEmail,
  mdiLogout,
  mdiThemeLightDark,
  mdiGithub,
  mdiReact
} from '@mdi/js'

export default [
  // {
  //   icon: mdiMenu,
  //   label: 'Sample menu',
  //   menu: [
  //     {
  //       icon: mdiClockOutline,
  //       label: 'Item One'
  //     },
  //     {
  //       icon: mdiCloud,
  //       label: 'Item Two'
  //     },
  //     {
  //       isDivider: true
  //     },
  //     {
  //       icon: mdiCrop,
  //       label: 'Item Last'
  //     }
  //   ]
  // },
  {
    menu: [
      {
        icon: mdiAccount,
        label: 'My Profile',
        to: '/profile'
      },
      {
        icon: mdiLock,
        label: 'Update Password',
        to: '/update-password'
      },
      {
        icon: mdiLogout,
        label: 'Logout',
        isLogout: true,
        accountHubView: true,
        companyHubView: true
      }
      // {
      //   icon: mdiCogOutline,
      //   label: 'Settings'
      // },
      // {
      //   icon: mdiEmail,
      //   label: 'Messages'
      // },
      // {
      //   isDivider: true
      // },
      // {
      //   icon: mdiLogout,
      //   label: 'Log Out',
      //   isLogout: true,
      //   accountHubView: true,
      //   companyHubView: true
      // }
    ]
  }
  // {
  //   icon: mdiThemeLightDark,
  //   label: 'Light/Dark',
  //   isDesktopNoLabel: true,
  //   isToggleLightDark: true
  // },
  // {
  //   icon: mdiGithub,
  //   label: 'GitHub',
  //   isDesktopNoLabel: true,
  //   href: 'https://github.com/justboil/admin-one-vue-tailwind',
  //   target: '_blank'
  // },
  // {
  //   icon: mdiReact,
  //   label: 'React version',
  //   isDesktopNoLabel: true,
  //   href: 'https://github.com/justboil/admin-one-react-tailwind',
  //   target: '_blank'
  // },
  // {
  //   icon: mdiLogout,
  //   label: 'Log out',
  //   isDesktopNoLabel: true,
  //   isLogout: true,
  //   accountHubView: true,
  //   companyHubView: true
  // }
]
