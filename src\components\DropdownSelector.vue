<template>
  <multiselect
    v-model="model"
    :options="options"
    label="name"
    :placeholder="props.placeholder ?? 'Select'"
  >
  </multiselect>
</template>

<script setup>
import { ref } from 'vue'
import Multiselect from 'vue-multiselect'

const props = defineProps(['options', 'placeholder'])

const model = defineModel()

const options = ref(props.options)
</script>

<style src="vue-multiselect/dist/vue-multiselect.css"></style>

<style>
.multiselect__option--highlight {
  background: #eee !important;
  outline: none;
  color: black;
}

.multiselect__option--highlight::after {
  content: attr(data-select);
  background: #eee !important;
  color: black;
}

.multiselect__input:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.125);
}
</style>
