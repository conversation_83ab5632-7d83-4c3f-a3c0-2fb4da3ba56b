<template>
  <LayoutAuthenticated>
    <SectionMainFull>
      <SectionTitleLineWithButton :icon="mdiAccount" title="My Profile" small>
        <BaseButton
          :icon="mdiArrowLeft"
          label="Back"
          color="contrast"
          @click="goBack()"
          rounded-full
          small
        />
      </SectionTitleLineWithButton>

      <CardBox padding="0" v-if="authUserData">
        <div class="grid grid-cols-1 mt-5 lg:grid-cols-2">
          <BaseLevel type="justify-around lg:justify-center">
            <div class="space-y-3 text-center md:text-left lg:mx-12">
              <div
                class="rounded-full flex justify-center mx-auto items-center text-center bg-gray-100 dark:bg-slate-800 h-40 w-40 text-6xl text-gray-500 font-thin"
              >
                {{ initials }}
              </div>
              <h1 class="text-2xl">
                <div class="text-2xl text-center">
                  <span>{{ authUserData?.name ?? '' }}</span>
                </div>
              </h1>
            </div>
          </BaseLevel>
          <div>
            <div>
              <div class="flex space-x-3 mb-5">
                <BaseIcon
                  :path="mdiCellphone"
                  size="20"
                  w=""
                  h=""
                  class="text-gray-600 border-gray-600"
                />
                <div>{{ authUserData?.phone_number ?? 'N/A' }}</div>
              </div>
              <div class="flex space-x-3 mb-5">
                <BaseIcon
                  :path="mdiEmailOutline"
                  size="20"
                  w=""
                  h=""
                  class="text-gray-600 border-gray-600"
                />
                <div>{{ authUserData?.email ?? 'N/A' }}</div>
              </div>
              <div class="flex space-x-3 mb-5">
                <BaseIcon
                  :path="mdiBriefcaseOutline"
                  size="20"
                  w=""
                  h=""
                  class="text-gray-600 border-gray-600"
                />
                <div>{{ authUserData?.role ?? 'N/A' }}</div>
              </div>
            </div>
          </div>
        </div>
      </CardBox>
    </SectionMainFull>
  </LayoutAuthenticated>
</template>
<script setup>
import BaseButton from '@/components/BaseButton.vue'
import BaseIcon from '@/components/BaseIcon.vue'
import BaseLevel from '@/components/BaseLevel.vue'
import CardBox from '@/components/CardBox.vue'
import SectionMainFull from '@/components/SectionMainFull.vue'
import SectionTitleLineWithButton from '@/components/SectionTitleLineWithButton.vue'
import LayoutAuthenticated from '@/layouts/LayoutAuthenticated.vue'
import { useAuthStore } from '@/stores/auth.js'
import { useUserStore } from '@/stores/user.js'
import {
  mdiAccount,
  mdiArrowLeft,
  mdiBriefcaseOutline,
  mdiCellphone,
  mdiEmailOutline
} from '@mdi/js'
import { computed, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()
const userStore = useUserStore()
const mainLoader = ref(false)

const initials = computed(() => {
  if (authUserData.value && authUserData.value.name) {
    return `${authUserData.value.name.substring(0, 1)}`
  } else {
    return 'N/A'
  }
})

const authUserData = computed(() => {
  return authStore.userData
})

const goBack = () => {
  router.go(-1)
}

onMounted(async () => {
  try {
    mainLoader.value = true
    await userStore.getUserDetails(route.params.id)
  } catch (e) {
  } finally {
    mainLoader.value = false
  }
})
</script>
<style scoped></style>
