import { defineStore } from 'pinia'
import { useRoute, useRouter } from 'vue-router'
import { BASE_URL, api } from '@/helpers/api/http-commons.js'
import axios from 'axios'

const router = useRouter()

export const useTransactionStore = defineStore('transaction', {
  state: () => {
    return {
      transactions: [],
      transaction: null,
      total_count: 0,
      total_amount: 0,
      total_successful_count: 0,
      total_successful_amount: 0,
      total_unsuccessful_count: 0,
      total_unsuccessful_amount: 0,
      total_pending_count: 0,
      total_pending_amount: 0,
      total_pages: 1,
      limit: 15
    }
  },
  getters: {},
  actions: {
    async getTransactions(page) {
      try {
        let config = {
          method: 'get',
          maxBodyLength: Infinity,
          url: BASE_URL + api.TRANSACTION.BASE + `?page=${page}&limit=${this.limit}`,
          headers: {
            'Content-Type': 'application/json'
          },
          params: {}
        }
        const response = await axios.request(config)
        this.transactions = response.data.data
        this.total_count = response.data.summary.totalCount
        this.total_amount = response.data.summary.totalAmount
        this.total_successful_count = response.data.summary.successfulCount
        this.total_successful_amount = response.data.summary.successfulAmount
        this.total_unsuccessful_count = response.data.summary.unsuccessfulCount
        this.total_unsuccessful_amount = response.data.summary.unsuccessfulAmount
        this.total_pending_count = response.data.summary.pendingCount
        this.total_pending_amount = response.data.summary.pendingAmount
        this.total_pages = response.data.total_pages
        return response.status
      } catch (error) {
        return error.response.status
      }
    },
    async getClientTransactions(id) {
      try {
        let config = {
          method: 'get',
          maxBodyLength: Infinity,
          url: BASE_URL + api.TRANSACTION.BASE + api.TRANSACTION.GET_CLIENT_TRANSACTIONS + id,
          headers: {
            'Content-Type': 'application/json'
          },
          params: {}
        }
        const response = await axios.request(config)
        this.transactions = response.data.data
        return response.status
      } catch (error) {
        return error.response.status
      }
    },
    async getTransaction(id) {
      try {
        let config = {
          method: 'get',
          maxBodyLength: Infinity,
          url: BASE_URL + api.TRANSACTION.BASE + '/' + id,
          headers: {
            'Content-Type': 'application/json'
          }
        }
        const response = await axios.request(config)
        this.transaction = response.data
        return response.status
      } catch (error) {
        return error.response.status
      }
    },
    async reconcileTransaction(data) {
      try {
        let config = {
          method: 'post',
          maxBodyLength: Infinity,
          url: BASE_URL + api.TRANSACTION.BASE + api.TRANSACTION.RECONCILE_TRANSACTION,
          data,
          headers: {
            'Content-Type': 'application/json'
          }
        }
        const response = await axios.request(config)
        return response.status
      } catch (error) {
        return error.response.status
      }
    },
    async refundTransaction(data) {
      try {
        let config = {
          method: 'post',
          maxBodyLength: Infinity,
          url: BASE_URL + api.TRANSACTION.BASE + api.TRANSACTION.REFUND_TRANSACTION,
          data,
          headers: {
            'Content-Type': 'application/json'
          }
        }
        const response = await axios.request(config)
        return response.status
      } catch (error) {
        return error.response.status
      }
    },
    async getClientFilteredTransactions(id, data) {
      try {
        let config = {
          method: 'post',
          maxBodyLength: Infinity,
          url:
            BASE_URL + api.TRANSACTION.BASE + api.TRANSACTION.GET_CLIENT_FILTERED_TRANSACTIONS + id,
          headers: {
            'Content-Type': 'application/json'
          },
          params: {},
          data: data
        }
        const response = await axios.request(config)
        this.transactions = response.data.data
        return response.status
      } catch (error) {
        return error.response.status
      }
    },
    async getFilteredTransactions(data) {
      try {
        let config = {
          method: 'post',
          maxBodyLength: Infinity,
          url: BASE_URL + api.TRANSACTION.BASE + api.TRANSACTION.FILTERED_TRANSACTIONS,
          headers: {
            'Content-Type': 'application/json'
          },
          params: {},
          data: { ...data, limit: this.limit }
        }
        const response = await axios.request(config)

        this.transactions = response.data.data
        this.total_count = response.data.summary.totalCount
        this.total_amount = response.data.summary.totalAmount
        this.total_successful_count = response.data.summary.successfulCount
        this.total_successful_amount = response.data.summary.successfulAmount
        this.total_unsuccessful_count = response.data.summary.unsuccessfulCount
        this.total_unsuccessful_amount = response.data.summary.unsuccessfulAmount
        this.total_pending_count = response.data.summary.pendingCount
        this.total_pending_amount = response.data.summary.pendingAmount
        this.total_pages = response.data.total_pages

        return response.status
      } catch (error) {
        return error.response.status
      }
    },
    async exportTransactions(data) {
      try {
        let config = {
          method: 'post',
          maxBodyLength: Infinity,
          url: BASE_URL + api.TRANSACTION.BASE + api.TRANSACTION.EXPORT,
          responseType: 'blob',
          headers: {
            'Content-Type': 'application/json'
          },
          params: {},
          data: data
        }
        return await axios.request(config)
      } catch (error) {
        return error.response
      }
    }
  }
})
