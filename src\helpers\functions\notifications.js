import { usePush } from 'notivue'

export function useSuccess() {
  const push = usePush()

  return (title, message, duration = 6000) =>
    push.success({
      title,
      message,
      duration
    })
}

export function usePiniaSuccess(title, message, duration = 6000) {
  const push = usePush()

  return () =>
    push.success({
      title: title,
      message: message,
      duration: duration
    })
}

export function useWarning() {
  const push = usePush()

  return (title, message, duration = 6000) =>
    push.warning({
      title,
      message,
      duration
    })
}

export function useInfo() {
  const push = usePush()

  return (title, message, duration = 6000) =>
    push.info({
      title,
      message,
      duration
    })
}
