import { createRouter, createWebHashHistory, createWebHistory } from 'vue-router'
import Style from '@/views/StyleView.vue'
import Home from '@/views/HomeView.vue'
import ClientRouteView from '@/views/auth/ClientRouteView.vue'

const routes = [
  // {
  //   meta: {
  //     title: 'Home'
  //   },
  //   path: '/',
  //   name: 'home',
  //   component: () => import('@/views/LandingView.vue')
  // },
  {
    path: '/',
    redirect: '/login'
  },
  {
    meta: {
      title: 'Login'
    },
    path: '/login',
    name: 'login',
    component: () => import('@/views/LoginView.vue')
  },
  {
    meta: {
      title: 'Forgot Password'
    },
    path: '/forgot-password',
    name: 'forgot-password',
    component: () => import('@/views/ForgotPasswordView.vue')
  },
  {
    meta: {
      title: 'Complete Forgot Password'
    },
    path: '/complete-forgot-password',
    name: 'complete-forgot-password',
    component: () => import('@/views/CompleteResetPasswordView.vue')
  },
  {
    meta: {
      title: 'Complete Email Change'
    },
    path: '/complete-email-change',
    name: 'complete-email-change',
    component: () => import('@/views/CompleteEmailChange.vue')
  },
  {
    meta: {
      title: 'Payment link'
    },
    path: '/payment-links/:id',
    name: 'payment-link',
    component: () => import('@/views/PaymentLinkView.vue')
  },
  {
    meta: {
      title: 'Company Hub',
      requiresAuth: true
    },
    path: '/company-hub',
    name: 'company-hub',
    component: () => import('@/views/auth/CompanyHomeView.vue'),
    children: [
      {
        meta: {
          title: 'Home',
          requiresAuth: true
        },
        path: 'home',
        name: 'home',
        component: () => import('@/views/auth/company/DashboardIndexView.vue')
      },
      {
        meta: {
          title: 'Accounts',
          requiresAuth: true
        },
        path: 'accounts',
        name: 'accounts',
        component: () => import('@/views/auth/company/AccountsIndexView.vue')
      },
      {
        meta: {
          title: 'Statistics',
          requiresAuth: true
        },
        path: 'statistics',
        name: 'statistics',
        component: () => import('@/views/auth/company/StatisticsView.vue')
      },
      {
        meta: {
          title: 'Users',
          requiresAuth: true
        },
        path: 'users',
        name: 'users',
        component: () => import('@/views/auth/company/UsersIndexView.vue')
      },
      {
        meta: {
          title: 'User Profile',
          requiresAuth: true
        },
        path: 'user-profile/:id',
        name: 'user-profile',
        component: () => import('@/views/auth/company/UserDetailsView.vue')
      },
      {
        meta: {
          title: 'Accounts Summary',
          requiresAuth: true
        },
        path: 'reports/accounts/summary',
        name: 'accounts-summary',
        component: () => import('@/views/auth/company/reports/AccountsSummary.vue')
      },
      {
        meta: {
          title: 'Recons',
          requiresAuth: true
        },
        path: 'recons',
        name: 'recons',
        redirect: '/company-hub/recons/card'
      },
      {
        meta: {
          title: 'Card Recons',
          requiresAuth: true
        },
        path: 'recons/card',
        name: 'card-recons',
        component: () => import('@/views/auth/company/recons/CardReconsView.vue')
      },
      {
        meta: {
          title: 'Mobile Recons',
          requiresAuth: true
        },
        path: 'recons/mobile',
        name: 'mobile-recons',
        component: () => import('@/views/auth/company/recons/MobileReconsView.vue')
      },
      {
        meta: {
          title: 'Recon Detail',
          requiresAuth: true
        },
        path: 'recons/:id',
        name: 'recon-detail',
        component: () => import('@/views/auth/company/recons/ReconView.vue')
      },
      {
        meta: {
          title: 'Recons Configs',
          requiresAuth: true
        },
        path: 'recons/configs',
        name: 'recons-configs',
        component: () => import('@/views/auth/company/recons/ReconsConfigsView .vue')
      },
      {
        meta: {
          title: 'Transaction Detail',
          requiresAuth: true
        },
        path: 'transaction/:id',
        name: 'transaction-detail',
        component: () => import('@/views/auth/company/TransactionView.vue')
      }
    ]
  },
  {
    meta: {
      title: 'Users',
      requiresAuth: true
    },
    path: '/company-users',
    name: 'company-users',
    component: () => import('@/views/auth/company/UsersIndexView.vue')
  },
  {
    meta: {
      title: 'Account',
      requiresAuth: true
    },
    path: '/account',
    name: 'account',
    component: ClientRouteView,
    children: [
      {
        meta: {
          title: 'Dashboard',
          requiresAuth: true
        },
        path: 'dashboard',
        name: 'dashboard',
        component: () => import('@/views/auth/DashboardIndexView.vue')
      },
      {
        meta: {
          title: 'Transactions',
          requiresAuth: true
        },
        path: 'transactions',
        name: 'transactions',
        component: () => import('@/views/auth/transaction/TransactionIndexView.vue')
      },
      {
        meta: {
          title: 'Payment links',
          requiresAuth: true
        },
        path: 'payment-links',
        name: 'payment-links',
        component: () => import('@/views/auth/PaymentLinksView.vue')
      },
      {
        meta: {
          title: 'Transaction Details',
          requiresAuth: true
        },
        path: 'transactions/:id',
        name: 'transaction-details',
        component: () => import('@/views/auth/transaction/TransactionView.vue')
      }
    ]
  },
  // {
  //   meta: {
  //     title: 'Dashboard',
  //     requiresAuth: true,
  //   },
  //   path: '/dashboard/:id',
  //   name: 'dashboard',
  //   component: () => import('@/views/HomeView.vue')
  // },
  {
    meta: {
      title: 'Tables'
    },
    path: '/tables',
    name: 'tables',
    component: () => import('@/views/TablesView.vue')
  },
  {
    meta: {
      title: 'Forms'
    },
    path: '/forms',
    name: 'forms',
    component: () => import('@/views/FormsView.vue')
  },
  {
    meta: {
      title: 'Profile'
    },
    path: '/profile',
    name: 'profile',
    component: () => import('@/views/ProfileView.vue')
  },
  {
    meta: {
      title: 'Update Password'
    },
    path: '/update-password',
    name: 'update-password',
    component: () => import('@/views/auth/company/UpdatePassword.vue')
  },
  {
    meta: {
      title: 'Ui'
    },
    path: '/ui',
    name: 'ui',
    component: () => import('@/views/UiView.vue')
  },
  {
    meta: {
      title: 'Responsive layout'
    },
    path: '/responsive',
    name: 'responsive',
    component: () => import('@/views/ResponsiveView.vue')
  },
  {
    meta: {
      title: 'Error'
    },
    path: '/error',
    name: 'error',
    component: () => import('@/views/ErrorView.vue')
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    return savedPosition || { top: 0 }
  }
})
router.beforeEach((to, from, next) => {
  if (to.meta.requiresAuth && !localStorage.getItem('auth')) {
    next('/login')
  } else {
    next()
  }
})

export default router
