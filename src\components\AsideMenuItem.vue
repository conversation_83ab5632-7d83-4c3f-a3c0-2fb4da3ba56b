<script setup>
import { ref, computed } from 'vue'
import { RouterLink } from 'vue-router'
import { mdiMinus, mdiPlus } from '@mdi/js'
import { getButtonColor } from '@/colors.js'
import BaseIcon from '@/components/BaseIcon.vue'
import AsideMenuList from '@/components/AsideMenuList.vue'
import { useRoute } from 'vue-router'
import Permissions from '@/helpers/permissions'
import { useAuthStore } from '@/stores/auth'

const props = defineProps({
  item: {
    type: Object,
    required: true
  },
  isDropdownList: Boolean
})
const route = useRoute()
const auth = useAuthStore()

const accountData = computed(() => {
  return auth.accountData
})

const userData = computed(() => {
  return auth.userData
})

const emit = defineEmits(['menu-click'])

const hasColor = computed(() => props.item && props.item.color)

const asideMenuItemActiveStyle = computed(() =>
  hasColor.value ? '' : 'aside-menu-item-active font-bold'
)

const isDropdownActive = ref(false)

const componentClass = computed(() => [
  props.isDropdownList ? 'py-2 px-6 text-sm' : 'py-2',
  hasColor.value
    ? getButtonColor(props.item.color, false, true)
    : `aside-menu-item dark:text-slate-300 dark:hover:text-white`
])

const hasDropdown = computed(() => !!props.item.menu)

const activeClass = computed(() => {
  const isActive = route.path === (props.item.to ?? props.item.href)
  return isActive
})

const companyHubView = computed(() => {
  if (route.name == 'company-hub' && props.item.companyHubView == true) {
    return true
  } else {
    return false
  }
})
const accountHubView = computed(() => {
  return props.item.accountHubView
})

const isRouteVisible = computed(() => {
  // Check for SUPERADMIN_ONLY permission
  if (props.item?.permission == Permissions.SUPERADMIN_ONLY) {
    if (userData.value?.role !== 'SUPERADMIN') {
      return false
    }
  }

  if (route.matched[0].name != 'company-hub' && props.item.accountHubView == true) {
    if (
      props.item?.permission == Permissions.CHECKOUT_SERVICES &&
      accountData.value?.checkout_services != 'ON'
    ) {
      return false
    }
    return true
  } else if (route.matched[0].name == 'company-hub' && props.item.companyHubView == true) {
    return true
  } else {
    return false
  }
})

const menuClick = (event) => {
  emit('menu-click', event, props.item)

  if (hasDropdown.value) {
    isDropdownActive.value = !isDropdownActive.value
  }
}
</script>

<template>
  <li v-if="isRouteVisible">
    <component
      :is="item.to ? RouterLink : 'a'"
      v-slot="vSlot"
      :to="item.to ?? null"
      :href="item.href ?? null"
      :target="item.target ?? null"
      class="flex items-center cursor-pointer"
      :class="[componentClass, activeClass ? 'bg-gray-600' : '']"
      @click="menuClick"
    >
      <BaseIcon
        v-if="item.icon"
        :path="item.icon"
        class="flex-none"
        :class="[vSlot && vSlot.isExactActive ? asideMenuItemActiveStyle : '']"
        w="w-10"
        :size="15"
      />
      <span
        class="grow text-ellipsis line-clamp-1 text-sm whitespace-nowrap"
        :class="[
          { 'pr-12': !hasDropdown },
          vSlot && vSlot.isExactActive ? asideMenuItemActiveStyle : ''
        ]"
        >{{ item.label }}</span
      >
      <BaseIcon
        v-if="hasDropdown"
        :path="isDropdownActive ? mdiMinus : mdiPlus"
        class="flex-none"
        :class="[vSlot && vSlot.isExactActive ? asideMenuItemActiveStyle : '']"
        w="w-12"
      />
    </component>
    <AsideMenuList
      v-if="hasDropdown"
      :menu="item.menu"
      :class="['aside-menu-dropdown', isDropdownActive ? 'block dark:bg-slate-800/50' : 'hidden']"
      is-dropdown-list
    />
  </li>
</template>
