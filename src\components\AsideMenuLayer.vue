<script setup>
import { mdiLogout, mdiClose } from '@mdi/js'
import { computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import AsideMenuList from '@/components/AsideMenuList.vue'
import AsideMenuItem from '@/components/AsideMenuItem.vue'
import BaseIcon from '@/components/BaseIcon.vue'
import Logo from '@/assets/images/logos/logo2.png'
import { useAuthStore } from '@/stores/auth.js'
import { logout } from '@/helpers/functions/auth.js'
import mainLogo from '@/assets/images/logos/logo2.png'
const router = useRouter()
const route = useRoute()
const auth = useAuthStore()

defineProps({
  menu: {
    type: Array,
    required: true
  }
})

const accountData = computed(() => {
  return auth.accountData
})
const companyData = computed(() => {
  return auth.companyData
})
const emit = defineEmits(['menu-click', 'aside-lg-close-click'])
const showBusinessName = computed(() => {
  if (route.matched[0].name != 'company-hub') {
    return true
  } else {
    return false
  }
})
const logoutItem = computed(() => ({
  label: 'Logout',
  icon: mdiLogout,
  color: 'info',
  isLogout: true,
  accountHubView: true,
  companyHubView: true
}))

const menuClick = (event, item) => {
  emit('menu-click', event, item)
  if (item.label == 'Logout') {
    logout()
  }
}

const asideLgCloseClick = (event) => {
  emit('aside-lg-close-click', event)
}
</script>

<template>
  <aside id="aside" class="w-52 fixed flex z-40 top-0 h-screen transition-position overflow-hidden">
    <div class="aside flex-1 flex flex-col overflow-hidden dark:bg-slate-900">
      <div
        class="flex flex-col justify-center items-center aside-brand border-white border-b-2 relative"
      >
        <div class="aside-brand dark:bg-slate-900 py-4">
          <div class="flex justify-center" v-if="companyData?.logo_url">
            <div class="Logo side-background h-14 overflow-hidden">
              <img
                class="object-contain block h-full"
                :src="companyData.logo_url"
                @error="imageLoadOnError"
              />
            </div>
          </div>
          <div class="flex justify-center" v-else>
            <div class="Logo side-background h-14 overflow-hidden">
              <img
                class="object-contain block h-full"
                :src="mainLogo"
                @error="imageLoadOnError"
              />
            </div>
          </div>
          <button
            class="hidden lg:inline-block xl:hidden p-1 absolute left-0 top-0"
            @click.prevent="asideLgCloseClick"
          >
            <BaseIcon :path="mdiClose" />
          </button>
        </div>
        <div
          v-if="showBusinessName"
          class="aside-brand text-white dark:text-slate-300 dark:hover:text-white dark:bg-slate-900 text-lg"
        >
          {{ accountData?.business_name ?? '' }}
        </div>
      </div>

      <div
        class="flex-1 overflow-y-auto overflow-x-hidden aside-scrollbars dark:aside-scrollbars-[slate]"
      >
        <AsideMenuList :menu="menu" @menu-click="menuClick" />
      </div>

      <ul>
        <AsideMenuItem :item="logoutItem" @menu-click="menuClick" />
      </ul>
    </div>
  </aside>
</template>
