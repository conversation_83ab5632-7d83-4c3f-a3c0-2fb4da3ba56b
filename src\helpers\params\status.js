export const STATUS = [
  {
    "No.": 1,
    "Code": 100,
    "Name": "Active Service",
    "Descriptions": "service that can be used for transacting"
  },
  {
    "No.": 2,
    "Code": 101,
    "Name": "Deactivated service",
    "Descriptions": "service that can not be used for transacting"
  },
  {
    "No.": 3,
    "Code": 200,
    "Name": "Active User",
    "Descriptions": "system active User"
  },
  {
    "No.": 4,
    "Code": 201,
    "Name": "Deactivated User",
    "Descriptions": "deactivated system user"
  },
  {
    "No.": 5,
    "Code": 283,
    "Name": "Awaiting Payment Confirmation",
    "Descriptions": "provided payload has missing parameters"
  },
  {
    "No.": 6,
    "Code": 278,
    "Name": "Pending Payment",
    "Descriptions": "provided payload has missing parameters"
  },
  {
    "No.": 7,
    "Code": 284,
    "Name": "Invalid Payload",
    "Descriptions": "provided payload has missing parameters"
  },
  {
    "No.": 8,
    "Code": 285,
    "Name": "Invalid Credentials",
    "Descriptions": "invalid user credentials provided"
  },
  {
    "No.": 9,
    "Code": 289,
    "Name": "transation failed",
    "Descriptions": "An unknown error occured"
  },
  {
    "No.": 10,
    "Code": 290,
    "Name": "Invalid Payer Number",
    "Descriptions": "Number formart Recieved is invalid"
  },
  {
    "No.": 11,
    "Code": 300,
    "Name": "Successful Payment",
    "Descriptions": "transaction successfully transacted"
  },
  {
    "No.": 12,
    "Code": 301,
    "Name": "Un-successful Payment",
    "Descriptions": "transaction was not successfully transacted"
  },
  {
    "No.": 13,
    "Code": 302,
    "Name": "Pending transaction",
    "Descriptions": "transaction In Progress"
  },
  {
    "No.": 14,
    "Code": 400,
    "Name": "Active Client",
    "Descriptions": "client that can perform transaction"
  },
  {
    "No.": 15,
    "Code": 401,
    "Name": "Deactivated Client",
    "Descriptions": "client that can not perform transaction"
  },
  {
    "No.": 16,
    "Code": 237,
    "Name": "New disbursement",
    "Descriptions": "newly added customer disbursment request"
  },
  {
    "No.": 17,
    "Code": 238,
    "Name": "Pending disbursement",
    "Descriptions": "Pending customer disbursment request"
  },
  {
    "No.": 18,
    "Code": 239,
    "Name": "Failed disbursement",
    "Descriptions": "failed customer disbursment request"
  },
  {
    "No.": 19,
    "Code": 240,
    "Name": "Successful disbursement",
    "Descriptions": "successfull customer disbursment request"
  },
  {
    "No.": 20,
    "Code": 304,
    "Name": "Refunded",
    "Descriptions": "amount was refunded to customer mobile acount"
  },
  {
    "No.": 21,
    "Code": 303,
    "Name": "Ambigious Transaction",
    "Descriptions": "Transaction left for manual reconciliation"
  }
];

