<script setup>
import { mdiCog } from '@mdi/js'
import CardBox from '@/components/CardBox.vue'
import NumberDynamic from '@/components/NumberDynamic.vue'
import CounterDynamic from '@/components/CounterDynamic.vue'
import BaseIcon from '@/components/BaseIcon.vue'
import BaseLevel from '@/components/BaseLevel.vue'
import PillTagTrend from '@/components/PillTagTrend.vue'
import BaseButton from '@/components/BaseButton.vue'

defineProps({
  containClass: {
    type: String,
    default: null
  },
  number: {
    type: String,
    default: 0
  },
  count: {
    type: Number,
    default: 0
  },
  icon: {
    type: String,
    default: null
  },
  prefix: {
    type: String,
    default: null
  },
  suffix: {
    type: String,
    default: null
  },
  label: {
    type: String,
    default: null
  },
  color: {
    type: String,
    default: null
  },
  trend: {
    type: String,
    default: null
  },
  trendType: {
    type: String,
    default: null
  }
})

function isValidNumber(str) {
  return !isNaN(str)
}
</script>

<template>
  <CardBox rounded="rounded" :class="containClass">
    <BaseLevel v-if="trend" class="mb-3" mobile>
      <PillTagTrend :trend="trend" :trend-type="trendType" small />
      <BaseButton :icon="mdiCog" icon-w="w-4" icon-h="h-4" color="lightDark" small />
    </BaseLevel>
    <BaseLevel mobile class="h-12">
      <div class="border flex items-center rounded-full p-2 bg-white">
        <BaseIcon v-if="icon" :path="icon" size="32" w="w-8" h="h-8" :class="color" />
      </div>
      <div class="">
        <h1 class="mt-1 text-md leading-tight" v-if="isValidNumber(number)">
          <NumberDynamic :value="number" :prefix="prefix" :suffix="suffix" />
        </h1>
        <h1 class="mt-1 text-md leading-tight" v-if="!isValidNumber(number)">
          {{ number }}
        </h1>
        <h4 class="text-sm leading-tight">
          {{ label }}
        </h4>
        <h1 v-if="count" class="flex justify-end text-md leading-tight">
          #<CounterDynamic :value="count" />
        </h1>
      </div>
    </BaseLevel>
  </CardBox>
</template>
