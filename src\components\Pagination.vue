<template>
  <vue-awesome-paginate
    :total-items="props.totalItems"
    :items-per-page="props.itemsPerPage"
    :max-pages-shown="5"
    v-model="model"
    @click="() => emits('click')"
  />
</template>

<script setup>
const model = defineModel()

const props = defineProps(['totalItems', 'itemsPerPage'])

const emits = defineEmits(['click'])
</script>

<style src="vue-multiselect/dist/vue-multiselect.css"></style>

<style>
.multiselect__option--highlight {
  background: #eee !important;
  outline: none;
  color: black;
}

.multiselect__option--highlight::after {
  content: attr(data-select);
  background: #eee !important;
  color: black;
}

.multiselect__input:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.125);
}

.pagination-container {
  display: flex;

  column-gap: 10px;
}

.paginate-buttons {
  height: 40px;

  width: 40px;

  border-radius: 20px;

  cursor: pointer;

  background-color: rgb(242, 242, 242);

  border: 1px solid rgb(217, 217, 217);

  color: black;
}

.paginate-buttons:hover {
  background-color: #d8d8d8;
}

.active-page {
  background-color: #3498db;

  border: 1px solid #3498db;

  color: white;
}

.active-page:hover {
  background-color: #2988c8;
}
</style>
