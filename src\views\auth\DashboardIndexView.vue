<template>
  <LayoutAuthenticated>
    <SectionMainFull>
      <!-- <SectionTitleLineWithButton :icon="mdiChartTimelineVariant" :title="`Overview ${timePeriod}`" small>
        <BaseButton :icon="mdiReload" color="whiteDark" @click="fillChartData" />
      </SectionTitleLineWithButton> -->
      <TransactionTimeDaily />

      <div class="grid grid-cols-1 gap-6 lg:grid-cols-4 mb-6">
        <CardBoxWidget
          trend-type="down"
          color="text-blue-500"
          bgColor="bg-red-500"
          :icon="mdiCash"
          :number="dailyStats.transactionsAmount"
          :count="dailyStats.transactionsCount"
          prefix=""
          label="Total Payments"
          containClass="text-right border"
        />
        <CardBoxWidget
          trend-type="down"
          color="text-primeGreen"
          containClass="bg-primeGreen text-white text-right"
          :icon="mdiCash"
          :number="dailyStats.successfulTransactionsAmount"
          :count="dailyStats.successfulTransactionsCount"
          prefix=""
          label="Successfull Payments"
        />
        <CardBoxWidget
          trend-type="down"
          color="text-primeTeal"
          :icon="mdiCash"
          :number="dailyStats.pendingTransactionsAmount"
          :count="dailyStats.pendingTransactionsCount"
          prefix=""
          label="Pending Payments"
          containClass="bg-primeTeal text-white text-right"
        />
        <CardBoxWidget
          trend-type="down"
          color="text-primeRed"
          :icon="mdiCash"
          :number="dailyStats.unsuccessfulTransactionsAmount"
          :count="dailyStats.unsuccessfulTransactionsCount"
          prefix=""
          label="Unsuccessful Payments"
          containClass="bg-primeRed text-white text-right"
        />
      </div>
      <SectionTitleLineWithButton :icon="mdiChartPie" title="Monthly overview">
        <BaseButton :icon="mdiReload" color="whiteDark" @click="fillChartData" />
      </SectionTitleLineWithButton>

      <CardBox class="mb-6">
        <h1 class="mb-4">Transactions Count</h1>
        <div v-if="monthlyChartData">
          <MonthlyTransactions :data="monthlyChartData" class="h-96" />
        </div>
        <TableSkeletonOne v-else />
      </CardBox>
      <CardBox class="mb-6">
        <h1 class="mb-4">Transactions Value</h1>
        <div v-if="monthlyChartData">
          <MonthlyTransactions :data="monthlyAmountChartData" class="h-96" />
        </div>
        <TableSkeletonOne v-else />
      </CardBox>
    </SectionMainFull>
  </LayoutAuthenticated>
</template>
<script setup>
import { computed, ref, onMounted } from 'vue'
import { useMainStore } from '@/stores/main'
import { useDashboardStore } from '@/stores/dashboard.js'
import { useAuthStore } from '@/stores/auth.js'
import {
  mdiAccountMultiple,
  mdiCash,
  mdiChartTimelineVariant,
  mdiMonitorCellphone,
  mdiReload,
  mdiGithub,
  mdiChartPie
} from '@mdi/js'
import * as chartConfig from '@/components/Charts/chart.config.js'
import * as monthlyConfig from '@/components/Charts/monthly-transactions-chart.config.js'
import LineChart from '@/components/Charts/LineChart.vue'
import SectionMain from '@/components/SectionMain.vue'
import SectionMainFull from '@/components/SectionMainFull.vue'
import CardBoxWidget from '@/components/CardBoxWidget.vue'
import CardBox from '@/components/CardBox.vue'
import TableSampleClients from '@/components/TableSampleClients.vue'
import NotificationBar from '@/components/NotificationBar.vue'
import BaseButton from '@/components/BaseButton.vue'
import CardBoxTransaction from '@/components/CardBoxTransaction.vue'
import CardBoxClient from '@/components/CardBoxClient.vue'
import LayoutAuthenticated from '@/layouts/LayoutAuthenticated.vue'
import SectionTitleLineWithButton from '@/components/SectionTitleLineWithButton.vue'
import MonthlyTransactions from '@/components/Charts/MonthlyTransactions.vue'
import SectionBannerStarOnGitHub from '@/components/SectionBannerStarOnGitHub.vue'
import TableSkeletonOne from '@/components/Reusables/Loaders/TableSkeletonOne.vue'
import TransactionTimeDaily from '@/components/Reusables/Breadcrumbs/TransactionTimeDaily.vue'

const authStore = useAuthStore()
const dashboardStore = useDashboardStore()
const chartData = ref(null)
const monthlyChartData = ref(null)
const monthlyAmountChartData = ref(null)
const accountData = computed(() => {
  return authStore.accountData
})

const fillChartData = async () => {
  await fetchDataAndInitializeCharts()
  await updateTimePeriod()
  chartData.value = chartConfig.sampleChartData()
}
const timePeriod = ref('')

const updateTimePeriod = () => {
  const now = new Date()
  const yesterday = new Date(now)
  yesterday.setDate(now.getDate() - 1)

  const options = { hour: 'numeric', minute: 'numeric', second: 'numeric' }
  const options1 = { hour: 'numeric', minute: 'numeric', second: 'numeric' }
  const yesterdayFormatted = yesterday.toLocaleTimeString('en-US', options)
  const nowFormatted = now.toLocaleTimeString('en-US', options)

  timePeriod.value = `From yesterday to ${nowFormatted} today`
}

const mainStore = useMainStore()

const clientBarItems = computed(() => mainStore.clients.slice(0, 4))

const transactionBarItems = computed(() => mainStore.history)
const dailyStats = computed(() => dashboardStore.dailyStats)
const monthlySuccess = computed(() => {
  var data = dashboardStore.monthlyStats
  return [
    data.jan.totalSuccessfulCount,
    data.feb.totalSuccessfulCount,
    data.mar.totalSuccessfulCount,
    data.apr.totalSuccessfulCount,
    data.may.totalSuccessfulCount,
    data.jun.totalSuccessfulCount,
    data.jul.totalSuccessfulCount,
    data.aug.totalSuccessfulCount,
    data.sep.totalSuccessfulCount,
    data.oct.totalSuccessfulCount,
    data.nov.totalSuccessfulCount,
    data.dec.totalSuccessfulCount
  ]
})

const monthlySuccessAmount = computed(() => {
  var data = dashboardStore.monthlyStats
  return [
    data.jan.totalSuccessfulAmount,
    data.feb.totalSuccessfulAmount,
    data.mar.totalSuccessfulAmount,
    data.apr.totalSuccessfulAmount,
    data.may.totalSuccessfulAmount,
    data.jun.totalSuccessfulAmount,
    data.jul.totalSuccessfulAmount,
    data.aug.totalSuccessfulAmount,
    data.sep.totalSuccessfulAmount,
    data.oct.totalSuccessfulAmount,
    data.nov.totalSuccessfulAmount,
    data.dec.totalSuccessfulAmount
  ]
})

const monthlyFailed = computed(() => {
  var data = dashboardStore.monthlyStats
  return [
    data.jan.totalUnsuccessfulCount,
    data.feb.totalUnsuccessfulCount,
    data.mar.totalUnsuccessfulCount,
    data.apr.totalUnsuccessfulCount,
    data.may.totalUnsuccessfulCount,
    data.jun.totalUnsuccessfulCount,
    data.jul.totalUnsuccessfulCount,
    data.aug.totalUnsuccessfulCount,
    data.sep.totalUnsuccessfulCount,
    data.oct.totalUnsuccessfulCount,
    data.nov.totalUnsuccessfulCount,
    data.dec.totalUnsuccessfulCount
  ]
})

const monthlyFailedAmount = computed(() => {
  var data = dashboardStore.monthlyStats
  return [
    data.jan.totalUnsuccessfulAmount,
    data.feb.totalUnsuccessfulAmount,
    data.mar.totalUnsuccessfulAmount,
    data.apr.totalUnsuccessfulAmount,
    data.may.totalUnsuccessfulAmount,
    data.jun.totalUnsuccessfulAmount,
    data.jul.totalUnsuccessfulAmount,
    data.aug.totalUnsuccessfulAmount,
    data.sep.totalUnsuccessfulAmount,
    data.oct.totalUnsuccessfulAmount,
    data.nov.totalUnsuccessfulAmount,
    data.dec.totalUnsuccessfulAmount
  ]
})

const monthlyTotal = computed(() => {
  var data = dashboardStore.monthlyStats
  return [
    data.jan.totalCount,
    data.feb.totalCount,
    data.mar.totalCount,
    data.apr.totalCount,
    data.may.totalCount,
    data.jun.totalCount,
    data.jul.totalCount,
    data.aug.totalCount,
    data.sep.totalCount,
    data.oct.totalCount,
    data.nov.totalCount,
    data.dec.totalCount
  ]
})

const monthlyTotalAmount = computed(() => {
  var data = dashboardStore.monthlyStats
  return [
    data.jan.totalAmount,
    data.feb.totalAmount,
    data.mar.totalAmount,
    data.apr.totalAmount,
    data.may.totalAmount,
    data.jun.totalAmount,
    data.jul.totalAmount,
    data.aug.totalAmount,
    data.sep.totalAmount,
    data.oct.totalAmount,
    data.nov.totalAmount,
    data.dec.totalAmount
  ]
})

const fetchDataAndInitializeCharts = async () => {
  try {
    await dashboardStore.getMonthlyClientStats(accountData.value.id)
    await dashboardStore.getDailyClientStats(accountData.value.id)

    const monthlyChartDataPromise = monthlyConfig.sampleChartData(
      monthlyTotal.value,
      monthlySuccess.value,
      monthlyFailed.value
    )
    const monthlyAmountChartDataPromise = monthlyConfig.sampleChartData(
      monthlyTotalAmount.value,
      monthlySuccessAmount.value,
      monthlyFailedAmount.value
    )

    monthlyAmountChartData.value = monthlyAmountChartDataPromise

    monthlyChartData.value = monthlyChartDataPromise
  } catch (error) {}
}
onMounted(async () => {
  fillChartData()
})
</script>
